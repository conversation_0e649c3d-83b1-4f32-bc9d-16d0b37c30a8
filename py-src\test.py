from config import *
import os
import csv
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import pymongo
from pymongo import UpdateOne
from pymongo import MongoClient
from tqdm import tqdm
import dotenv
from bson import ObjectId
import math
from csv_parser import csv_parser, CsvModel
from mongo_db import MongoDBManager


class CsvOrder(CsvModel):
    """Class to represent a row from the CSV file"""
    CSV_COLUMN_MAP =  {
        'order_id': 'INVOICE_ID',
        'amount': 'AMOUNT',
    }
    
    def __init__(self, row_data: Dict[str, str], index: int):
        super().__init__(row_data, index)
        self.amount = float(self.amount) if self.amount else 0
       

        
def main():
    """Main entry point"""
    # Parse CSV data
    csv_path = os.path.join(DATA_FOLDER, 'raw-invoice-amount.csv')
    orders_data = csv_parser(CsvOrder, csv_path)
    print(orders_data[47287]) # 47287
    
    order_ids = [int(row.order_id) for row in orders_data]
    mongo_helper = MongoDBManager(uri=MONGO_CONFIG['uri'], database=MONGO_CONFIG['database'])
    mongo_helper.connect()
    invoices = mongo_helper.find_all('invoices', {  'platform': "migration", 'orderId': {'$in': order_ids}, 'invoiceDate': {'$gte': datetime(2025, 10, 1), '$lte': datetime(2025, 10, 31)}}, {'_id': 1, 'orderId': 1, 'grandTotal': 1})
    
    invoice_map = {int(inv['orderId']): inv['grandTotal'] for inv in invoices}
    
    mistmatches = 0
    diffAmount = 0
    for row in orders_data:
        if int(row.order_id) in invoice_map:
            if row.amount != invoice_map[int(row.order_id)]:
                print(row.order_id, row.amount, invoice_map[int(row.order_id)], row.amount - invoice_map[int(row.order_id)])
                mistmatches += 1
                diffAmount += row.amount - invoice_map[int(row.order_id)]                
       
    print(f"Total mistmatches: {mistmatches}")
    print(f"Total diff amount: {diffAmount}")
                
    
main()