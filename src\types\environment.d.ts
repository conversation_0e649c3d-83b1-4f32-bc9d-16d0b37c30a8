declare global {
    namespace NodeJS {
        interface ProcessEnv {
            DATA_FOLDER: string;
            MONGODB_URI: string;
            ORGANIZATION_ID: string;
            DATABASE_NAME: string;
            MONGO_DEBUG: string;
            NODE_ENV: string;
            AWS_ACCESS_KEY_ID: string;
            AWS_SECRET_ACCESS_KEY: string;
            AWS_S3_BUCKET: string;
            AWS_S3_REGION: string;
            S3_ENDPOINT: string;
        }
    }
}

// If this file has no import/export statements (i.e. is a script)
// convert it into a module by adding an empty export statement.
export { }