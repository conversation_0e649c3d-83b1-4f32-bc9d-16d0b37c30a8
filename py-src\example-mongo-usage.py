#!/usr/bin/env python3
"""
Example usage of the MongoDBManager class from mongo-db.py
"""

import sys
import os
from datetime import datetime
from bson import ObjectId

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the MongoDBManager class
import importlib.util
spec = importlib.util.spec_from_file_location("mongo_db", "py-src/mongo-db.py")
mongo_db = importlib.util.module_from_spec(spec)
spec.loader.exec_module(mongo_db)

MongoDBManager = mongo_db.MongoDBManager

def example_usage():
    """
    Example of how to use the MongoDBManager class
    """
    print("MongoDB Manager Example Usage")
    print("=" * 40)
    
    # Initialize the manager
    db_manager = MongoDBManager()
    
    try:
        # Connect to database
        print("1. Connecting to MongoDB...")
        if not db_manager.connect():
            print("Failed to connect to database")
            return
        
        # Example 1: Count documents
        print("\n2. Counting documents...")
        purchase_count = db_manager.count_documents('purchases')
        pricing_count = db_manager.count_documents('pricings')
        print(f"   Purchases: {purchase_count}")
        print(f"   Pricings: {pricing_count}")
        
        # Example 2: Find documents with projection
        print("\n3. Finding sample purchases...")
        sample_purchases = db_manager.find_all(
            'purchases', 
            filter_query={},
            projection={'_id': 1, 'packageId': 1, 'startDate': 1, 'endDate': 1},
            limit=3
        )
        print(f"   Found {len(sample_purchases)} sample purchases")
        for purchase in sample_purchases:
            print(f"   - ID: {purchase['_id']}, Package: {purchase.get('packageId')}")
        
        # Example 3: Find one document
        print("\n4. Finding one pricing document...")
        sample_pricing = db_manager.find_one(
            'pricings',
            filter_query={},
            projection={'_id': 1, 'name': 1, 'expiredInDays': 1, 'durationUnit': 1}
        )
        if sample_pricing:
            print(f"   Found pricing: {sample_pricing.get('name', 'Unknown')}")
            print(f"   Expires in: {sample_pricing.get('expiredInDays')} {sample_pricing.get('durationUnit')}")
        
        # Example 4: Aggregation query
        print("\n5. Running aggregation query...")
        pipeline = [
            {'$group': {
                '_id': '$durationUnit',
                'count': {'$sum': 1},
                'avg_expiry': {'$avg': '$expiredInDays'}
            }},
            {'$sort': {'count': -1}}
        ]
        
        duration_stats = db_manager.aggregate('pricings', pipeline)
        print("   Duration unit statistics:")
        for stat in duration_stats:
            print(f"   - {stat['_id']}: {stat['count']} items, avg {stat['avg_expiry']:.1f} units")
        
        # Example 5: Find by ID (if we have a sample)
        if sample_purchases:
            print("\n6. Finding purchase by ID...")
            purchase_id = str(sample_purchases[0]['_id'])
            found_purchase = db_manager.find_by_id('purchases', purchase_id)
            if found_purchase:
                print(f"   Found purchase by ID: {purchase_id}")
        
        print("\n✓ All examples completed successfully!")
        
    except Exception as e:
        print(f"Error during example execution: {e}")
    
    finally:
        # Always close the connection
        db_manager.close()

def example_bulk_operations():
    """
    Example of bulk operations (commented out to avoid actual modifications)
    """
    print("\nBulk Operations Example (Commented)")
    print("=" * 40)
    
    print("""
    # Example bulk operations (for reference):
    
    from pymongo import UpdateOne, InsertOne
    
    # Bulk update operations
    bulk_ops = [
        UpdateOne(
            {'_id': ObjectId('...')},
            {'$set': {'endDate': datetime.now()}}
        ),
        UpdateOne(
            {'_id': ObjectId('...')},
            {'$set': {'isExpired': True}}
        )
    ]
    
    # Execute bulk operations
    result = db_manager.bulk_write('purchases', bulk_ops)
    print(f"Modified: {result['modified_count']} documents")
    
    # Bulk insert example
    new_documents = [
        {'name': 'Test Document 1', 'created': datetime.now()},
        {'name': 'Test Document 2', 'created': datetime.now()}
    ]
    
    inserted_ids = db_manager.insert_many('test_collection', new_documents)
    print(f"Inserted {len(inserted_ids)} documents")
    """)

if __name__ == "__main__":
    print("MongoDB Manager Usage Examples")
    print("This script demonstrates how to use the MongoDBManager class")
    print("Make sure your MongoDB connection is configured in config.py\n")
    
    try:
        example_usage()
        example_bulk_operations()
    except KeyboardInterrupt:
        print("\nExample interrupted by user")
    except Exception as e:
        print(f"Example failed: {e}")
