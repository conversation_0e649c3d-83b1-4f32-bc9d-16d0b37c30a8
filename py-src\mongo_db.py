from __future__ import annotations
import os
import csv
import argparse
from typing import List, Dict, Optional, Any
from datetime import datetime
import pymysql
import re
import PyPDF2
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import dotenv
from pymongo import MongoClient, UpdateOne, InsertOne, DeleteOne, ReplaceOne
from pymongo.errors import BulkWriteError, PyMongoError
from bson import ObjectId
from config import *

dotenv.load_dotenv()

class MongoDBManager:
    """
    Comprehensive MongoDB manager class for database operations
    """

    def __init__(self, uri: str = None, database: str = None):
        """
        Initialize MongoDB manager

        Args:
            uri: MongoDB connection URI (defaults to config)
            database: Database name (defaults to config)
        """
        self.uri = uri or MONGO_CONFIG['uri']
        self.database_name = database or MONGO_CONFIG['database']
        self.client = None
        self.db = None

    def connect(self) -> bool:
        """
        Connect to MongoDB

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.client = MongoClient(self.uri)
            self.db = self.client[self.database_name]
            # Test connection
            self.client.admin.command('ping')
            print(f"Successfully connected to MongoDB: {self.database_name}")
            return True
        except Exception as e:
            print(f"Failed to connect to MongoDB: {e}")
            return False

    def close(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            print("MongoDB connection closed")

    def get_collection(self, collection_name: str):
        """
        Get a collection from the database

        Args:
            collection_name: Name of the collection

        Returns:
            Collection object
        """
        if self.db is None:
            raise Exception("Database not connected. Call connect() first.")
        return self.db[collection_name]

    def find_all(self, collection_name: str, filter_query: Dict = None,
                 projection: Dict = None, sort: List = None, limit: int = None) -> List[Dict]:
        """
        Find all documents in a collection

        Args:
            collection_name: Name of the collection
            filter_query: MongoDB filter query
            projection: Fields to include/exclude
            sort: Sort criteria
            limit: Maximum number of documents to return

        Returns:
            List of documents
        """
        try:
            collection = self.get_collection(collection_name)
            cursor = collection.find(filter_query or {}, projection)
            if sort:
                cursor = cursor.sort(sort)
            if limit:
                cursor = cursor.limit(limit)

            return list(cursor)
        except Exception as e:
            print(f"Error finding documents in {collection_name}: {e}")
            return []

    def find_one(self, collection_name: str, filter_query: Dict,
                 projection: Dict = None) -> Optional[Dict]:
        """
        Find one document in a collection

        Args:
            collection_name: Name of the collection
            filter_query: MongoDB filter query
            projection: Fields to include/exclude

        Returns:
            Document or None
        """
        try:
            collection = self.get_collection(collection_name)
            return collection.find_one(filter_query, projection)
        except Exception as e:
            print(f"Error finding document in {collection_name}: {e}")
            return None

    def find_by_id(self, collection_name: str, document_id: str,
                   projection: Dict = None) -> Optional[Dict]:
        """
        Find document by ObjectId

        Args:
            collection_name: Name of the collection
            document_id: String representation of ObjectId
            projection: Fields to include/exclude

        Returns:
            Document or None
        """
        try:
            return self.find_one(collection_name, {'_id': ObjectId(document_id)}, projection)
        except Exception as e:
            print(f"Error finding document by ID in {collection_name}: {e}")
            return None

    def count_documents(self, collection_name: str, filter_query: Dict = None) -> int:
        """
        Count documents in a collection

        Args:
            collection_name: Name of the collection
            filter_query: MongoDB filter query

        Returns:
            Number of documents
        """
        try:
            collection = self.get_collection(collection_name)
            return collection.count_documents(filter_query or {})
        except Exception as e:
            print(f"Error counting documents in {collection_name}: {e}")
            return 0

    def insert_one(self, collection_name: str, document: Dict) -> Optional[str]:
        """
        Insert one document

        Args:
            collection_name: Name of the collection
            document: Document to insert

        Returns:
            Inserted document ID or None
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.insert_one(document)
            return str(result.inserted_id)
        except Exception as e:
            print(f"Error inserting document in {collection_name}: {e}")
            return None

    def insert_many(self, collection_name: str, documents: List[Dict]) -> List[str]:
        """
        Insert multiple documents

        Args:
            collection_name: Name of the collection
            documents: List of documents to insert

        Returns:
            List of inserted document IDs
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.insert_many(documents)
            return [str(id) for id in result.inserted_ids]
        except Exception as e:
            print(f"Error inserting documents in {collection_name}: {e}")
            return []

    def update_one(self, collection_name: str, filter_query: Dict,
                   update_query: Dict, upsert: bool = False) -> bool:
        """
        Update one document

        Args:
            collection_name: Name of the collection
            filter_query: Filter to match document
            update_query: Update operations
            upsert: Create document if not found

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.update_one(filter_query, update_query, upsert=upsert)
            return result.modified_count > 0 or (upsert and result.upserted_id)
        except Exception as e:
            print(f"Error updating document in {collection_name}: {e}")
            return False

    def update_many(self, collection_name: str, filter_query: Dict,
                    update_query: Dict) -> int:
        """
        Update multiple documents

        Args:
            collection_name: Name of the collection
            filter_query: Filter to match documents
            update_query: Update operations

        Returns:
            Number of documents modified
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.update_many(filter_query, update_query)
            return result.modified_count
        except Exception as e:
            print(f"Error updating documents in {collection_name}: {e}")
            return 0

    def update_by_id(self, collection_name: str, document_id: str,
                     update_query: Dict) -> bool:
        """
        Update document by ObjectId

        Args:
            collection_name: Name of the collection
            document_id: String representation of ObjectId
            update_query: Update operations

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.update_one(collection_name, {'_id': ObjectId(document_id)}, update_query)
        except Exception as e:
            print(f"Error updating document by ID in {collection_name}: {e}")
            return False

    def delete_one(self, collection_name: str, filter_query: Dict) -> bool:
        """
        Delete one document

        Args:
            collection_name: Name of the collection
            filter_query: Filter to match document

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.delete_one(filter_query)
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting document in {collection_name}: {e}")
            return False

    def delete_many(self, collection_name: str, filter_query: Dict) -> int:
        """
        Delete multiple documents

        Args:
            collection_name: Name of the collection
            filter_query: Filter to match documents

        Returns:
            Number of documents deleted
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.delete_many(filter_query)
            return result.deleted_count
        except Exception as e:
            print(f"Error deleting documents in {collection_name}: {e}")
            return 0

    def delete_by_id(self, collection_name: str, document_id: str) -> bool:
        """
        Delete document by ObjectId

        Args:
            collection_name: Name of the collection
            document_id: String representation of ObjectId

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.delete_one(collection_name, {'_id': ObjectId(document_id)})
        except Exception as e:
            print(f"Error deleting document by ID in {collection_name}: {e}")
            return False

    def bulk_write(self, collection_name: str, operations: List) -> Dict:
        """
        Perform bulk write operations

        Args:
            collection_name: Name of the collection
            operations: List of bulk operations (UpdateOne, InsertOne, etc.)

        Returns:
            Dictionary with operation results
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.bulk_write(operations)
            return {
                'inserted_count': result.inserted_count,
                'modified_count': result.modified_count,
                'deleted_count': result.deleted_count,
                'upserted_count': result.upserted_count,
                'matched_count': result.matched_count
            }
        except BulkWriteError as bwe:
            print(f"Bulk write error in {collection_name}: {bwe.details}")
            raise Exception(bwe.details)
        except Exception as e:
            print(f"Error in bulk write for {collection_name}: {e}")
            raise Exception(e)

    def aggregate(self, collection_name: str, pipeline: List[Dict]) -> List[Dict]:
        """
        Perform aggregation query

        Args:
            collection_name: Name of the collection
            pipeline: Aggregation pipeline

        Returns:
            List of aggregation results
        """
        try:
            collection = self.get_collection(collection_name)
            return list(collection.aggregate(pipeline))
        except Exception as e:
            print(f"Error in aggregation for {collection_name}: {e}")
            return []

    def create_index(self, collection_name: str, index_spec, **kwargs) -> bool:
        """
        Create index on collection

        Args:
            collection_name: Name of the collection
            index_spec: Index specification
            **kwargs: Additional index options

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = self.get_collection(collection_name)
            collection.create_index(index_spec, **kwargs)
            return True
        except Exception as e:
            print(f"Error creating index in {collection_name}: {e}")
            return False

    def drop_collection(self, collection_name: str) -> bool:
        """
        Drop a collection

        Args:
            collection_name: Name of the collection

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = self.get_collection(collection_name)
            collection.drop()
            return True
        except Exception as e:
            print(f"Error dropping collection {collection_name}: {e}")
            return False

def connect_to_mongo() -> MongoClient:
    """
    Legacy function for backward compatibility
    Connect to MongoDB and return client
    """
    client = MongoClient(MONGO_CONFIG['uri'])
    print(f"Connected to: {MONGO_CONFIG['uri']}")
    print(f"Database: {MONGO_CONFIG['database']}")
    return client

def get_database(client: MongoClient = None, database_name: str = None):
    """
    Get database instance

    Args:
        client: MongoDB client (optional)
        database_name: Database name (optional)

    Returns:
        Database instance
    """
    if not client:
        client = connect_to_mongo()

    db_name = database_name or MONGO_CONFIG['database']
    return client[db_name]
