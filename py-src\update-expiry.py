
import os
import csv
import argparse
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import pymongo
from pymongo import UpdateOne
from pymongo import MongoClient
from tqdm import tqdm
import dotenv
from bson import ObjectId

from config import *
# import mongo_db  # Not needed for this script
from mongo_db import MongoDBManager

# Load environment variables
dotenv.load_dotenv()


def calculate_end_date(start_date: datetime, expired_in_days: int, duration_unit: str) -> datetime:
    """
    Calculate end date based on start date, expiry value and duration unit

    Args:
        start_date: The start date
        expired_in_days: The expiry value from pricing
        duration_unit: The duration unit ('days', 'months', 'years')

    Returns:
        The calculated end date
    """
    if not start_date:
        return None

    end_date = start_date

    if duration_unit == 'days':
        end_date = start_date + timedelta(days=expired_in_days)
    elif duration_unit == 'months':
        # Add months by adding days (approximate)
        # For more accurate month calculation, we could use dateutil.relativedelta
        days_to_add = expired_in_days * 30  # Approximate days in a month
        end_date = start_date + timedelta(days=days_to_add)
    elif duration_unit == 'years':
        # Add years by adding days (approximate)
        days_to_add = expired_in_days * 365  # Approximate days in a year
        end_date = start_date + timedelta(days=days_to_add)
    else:
        print(f"Unknown duration unit: {duration_unit}, defaulting to days")
        end_date = start_date + timedelta(days=expired_in_days)

    return end_date

def update_purchase_expiry(dry_run: bool = False):
    """
    Main function to update purchase expiry dates

    Args:
        dry_run: If True, only simulate the updates without actually modifying the database
    """
    # mongo_helper = MongoDBHelper()
    mongo_helper = MongoDBManager(uri=MONGO_CONFIG['uri'], database=MONGO_CONFIG['database'])

    try:
        # Connect to MongoDB
        if not mongo_helper.connect():
            return

        # Get total counts first
        print("Getting total document counts...")
        total_purchases = mongo_helper.count_documents('purchases', {'organizationId': ORGANIZATION_ID})
        total_pricings = mongo_helper.count_documents('pricings', {'organizationId': ORGANIZATION_ID})

        print(f"Total purchases to process: {total_purchases}")
        print(f"Total pricing records: {total_pricings}")

        # Build pricing map in batches
        print("Building pricing map in batches...")
        pricing_map = {}
        batch_size = 1000
        last_pricing_id = None

        with tqdm(total=total_pricings, desc="Loading pricing data") as pbar:
            while True:
                # Build filter for current batch
                filter_query = {'organizationId': ORGANIZATION_ID}
                if last_pricing_id:
                    filter_query['_id'] = {'$gt': last_pricing_id}

                pricing_batch = mongo_helper.find_all('pricings', filter_query, projection={
                    '_id': 1,
                    'expiredInDays': 1,
                    'durationUnit': 1,
                    'services': 1
                }, sort=[('_id', 1)], limit=batch_size)

                if not pricing_batch:
                    break

                for pricing in pricing_batch:
                    pricing_map[str(pricing['_id'])] = {
                        'expiredInDays': pricing.get('expiredInDays', 0),
                        'durationUnit': pricing.get('durationUnit', 'days'),
                        'sessionCount': pricing.get('services', {}).get('sessionCount', 0),
                        'dayPassLimit': pricing.get('services', {}).get('dayPassLimit', 0),
                        'sessionType': pricing.get('services', {}).get('sessionType'),
                        'sessionPerDay': pricing.get('services', {}).get('sessionPerDay', 0)
                    }

                pbar.update(len(pricing_batch))

                # If we got a full batch, prepare for next iteration
                if len(pricing_batch) == batch_size:
                    last_pricing_id = pricing_batch[-1]['_id']
                else:
                    break

        print(f"Loaded {len(pricing_map)} pricing records into memory")

        print(f"Processing purchases and calculating new expiry dates{'(DRY RUN)' if dry_run else ''}...")

        # Process purchases in batches
        bulk_operations = []
        updated_count = 0
        skipped_count = 0
        last_purchase_id = None

        with tqdm(total=total_purchases, desc="Processing purchases") as pbar:
            while True:
                # Build filter for current batch
                # filter_query = {'organizationId': ORGANIZATION_ID}
                filter_query = {'organizationId': ORGANIZATION_ID}
                if last_purchase_id:
                    filter_query['_id'] = {'$gt': last_purchase_id}

                purchase_batch = mongo_helper.find_all('purchases', filter_query, projection={
                    '_id': 1,
                    'packageId': 1,
                    'startDate': 1,
                    'endDate': 1,
                    'isExpired': 1,
                    'isActive': 1,
                    'purchaseDate': 1
                }, sort=[('_id', 1)], limit=batch_size)

                if not purchase_batch:
                    break

                for purchase in purchase_batch:
                    try:
                        package_id = str(purchase.get('packageId'))
                        start_date = purchase.get('startDate') or purchase.get('purchaseDate')

                        if not package_id or package_id not in pricing_map:
                            print(f"Skipping purchase {purchase['_id']}: No pricing data found for package {package_id}")
                            skipped_count += 1
                            continue

                        if not start_date:
                            print(f"Skipping purchase {purchase['_id']}: No start date or purchase date found")
                            skipped_count += 1
                            continue

                        pricing_data = pricing_map[package_id]
                        expired_in_days = pricing_data['expiredInDays']
                        duration_unit = pricing_data['durationUnit']
                        # session_count = pricing_data['sessionCount']
                        # day_pass_limit = pricing_data['dayPassLimit']
                        # sessions_per_day = pricing_data['sessionPerDay']

                        # Calculate new end date
                        new_end_date = calculate_end_date(start_date, expired_in_days, duration_unit)
                        is_expired = new_end_date < datetime.now()
                        if new_end_date:
                            if not dry_run:
                                # Add update operation to bulk operations
                                bulk_operations.append(
                                    UpdateOne(
                                        {'_id': purchase['_id']},
                                        {'$set': {
                                            'endDate': new_end_date,
                                            'isExpired': is_expired,
                                            'isActive': not is_expired,
                                        }}
                                            
                                            
                                            
                                            
                                            
                                            
                                            # "sessionConsumed": 0,
                                            # 'totalSessions': session_count,
                                            # 'dayPassLimit': day_pass_limit,
                                            # 'sessionPerDay': sessions_per_day,
                                    )
                                )
                            updated_count += 1

                        # Execute bulk operations in batches of 1000 (only if not dry run)
                        if not dry_run and len(bulk_operations) >= 1000:
                            result = mongo_helper.bulk_write('purchases', bulk_operations)
                            bulk_operations = []

                    except Exception as e:
                        print(f"Error processing purchase {purchase.get('_id', 'unknown')}: {e}")
                        skipped_count += 1
                        continue

                pbar.update(len(purchase_batch))

                # If we got a full batch, prepare for next iteration
                if len(purchase_batch) == batch_size:
                    last_purchase_id = purchase_batch[-1]['_id']
                else:
                    break

        # Execute remaining bulk operations (only if not dry run)
        if not dry_run and bulk_operations:
            result = mongo_helper.bulk_write('purchases', bulk_operations)
            print(f"Final batch update completed: {result.get('modified_count', "KeyError")} documents updated")

        print(f"\n{'Dry run' if dry_run else 'Update'} completed!")
        print(f"Total purchases processed: {updated_count + skipped_count}")
        print(f"Would be updated: {updated_count}" if dry_run else f"Successfully updated: {updated_count}")
        print(f"Skipped: {skipped_count}")

    except Exception as e:
        print(f"Error during update process: {e}")
    finally:
        mongo_helper.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Update purchase expiry dates based on pricing data')
    parser.add_argument('--dry-run', action='store_true', help='Run in dry-run mode (no actual updates)')

    args = parser.parse_args()

    if args.dry_run:
        print("Running in dry-run mode - no actual updates will be performed")

    update_purchase_expiry(dry_run=args.dry_run)
