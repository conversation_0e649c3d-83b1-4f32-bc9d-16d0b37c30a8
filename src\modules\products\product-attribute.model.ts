import mongoose, { Schema, Document, Types } from 'mongoose';
import { Attributes } from '../../common/enums/enums';

export interface ProductAttribute {
    key: Attributes;
    value: string | Types.ObjectId | string[] | Types.ObjectId[];
}

export const ProductAttributeSchema = new Schema<ProductAttribute>(
    {
        key: {
            type: String,
            enum: Object.values(Attributes),
            required: true,
        },
        value: {
            type: Schema.Types.Mixed, // Accepts various value types
            required: true,
        },
    },
    { _id: false } // Indicates this schema will be embedded, not used as a standalone collection
);
