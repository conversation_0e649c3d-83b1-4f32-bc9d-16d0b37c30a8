# Update Purchase Expiry Script

This script updates the expiry dates of purchases in the MongoDB database based on pricing information.

## Overview

The script performs the following operations:

1. **Fetches all purchases** from the `purchases` collection
2. **Fetches all pricing data** from the `pricings` collection  
3. **Calculates new expiry dates** based on:
   - Purchase start date (or purchase date if start date is not available)
   - Pricing `expiredInDays` value
   - Pricing `durationUnit` (days, months, years)
4. **Updates the `endDate` field** in purchase documents

## Database Schema

### Purchase Collection Fields Used:
- `_id`: Document identifier
- `packageId`: Reference to pricing document
- `startDate`: Start date of the purchase
- `purchaseDate`: Fallback if startDate is not available
- `endDate`: Field that gets updated with calculated expiry

### Pricing Collection Fields Used:
- `_id`: Document identifier
- `expiredInDays`: Number of units for expiry calculation
- `durationUnit`: Unit type ('days', 'months', 'years')

## Usage

### Basic Usage
```bash
python py-src/update-expiry.py
```

### Dry Run Mode
```bash
python py-src/update-expiry.py --dry-run
```

## Configuration

The script uses configuration from `config.py`:

```python
MONGO_CONFIG = {
    'uri': 'mongodb://localhost:27017',
    'database': 'your_database_name'
}
```

Make sure to set the `MONGODB_URI` and `DATABASE_NAME` environment variables or update the config file.

## Features

### MongoDBHelper Class
The script includes a comprehensive `MongoDBHelper` class with methods for:
- Database connection management
- Document querying (find_all, find_one)
- Bulk operations
- Error handling

### Expiry Calculation
The `calculate_end_date()` function handles:
- **Days**: Direct addition of days
- **Months**: Approximate calculation (30 days per month)
- **Years**: Approximate calculation (365 days per year)
- **Unknown units**: Defaults to days

### Batch Processing
- Processes updates in batches of 1000 documents
- Shows progress with tqdm progress bar
- Provides detailed logging of operations

## Error Handling

The script includes comprehensive error handling:
- Database connection failures
- Missing pricing data
- Invalid date calculations
- Bulk operation errors

## Output

The script provides detailed output including:
- Connection status
- Number of purchases and pricing records found
- Progress during processing
- Final statistics:
  - Total purchases processed
  - Successfully updated count
  - Skipped count with reasons

## Example Output

```
Connected to MongoDB: your_database
Fetching all purchases...
Found 1500 purchases
Fetching all pricing data...
Found 250 pricing records
Processing purchases and calculating new expiry dates...
Processing purchases: 100%|████████████| 1500/1500 [00:30<00:00, 50.00it/s]
Batch update completed: 1000 documents updated
Final batch update completed: 500 documents updated

Update completed!
Total purchases processed: 1500
Successfully updated: 1450
Skipped: 50
MongoDB connection closed
```

## Dependencies

- pymongo
- python-dotenv
- tqdm
- bson

## Testing

Run the test suite:
```bash
python py-src/test-update-expiry.py
```

## MongoDB Helper Module

The script also enhances `mongo-db.py` with a comprehensive `MongoDBManager` class that provides:

### Core Operations
- `connect()` / `close()`: Connection management
- `find_all()` / `find_one()`: Document retrieval
- `insert_one()` / `insert_many()`: Document insertion
- `update_one()` / `update_many()`: Document updates
- `delete_one()` / `delete_many()`: Document deletion

### Advanced Operations
- `bulk_write()`: Bulk operations
- `aggregate()`: Aggregation queries
- `create_index()`: Index management
- `count_documents()`: Document counting

### Utility Methods
- `find_by_id()` / `update_by_id()` / `delete_by_id()`: ObjectId-based operations
- Comprehensive error handling
- Type hints for better IDE support

## Notes

- The script uses approximate calculations for months and years
- For more precise date calculations, consider using `dateutil.relativedelta`
- Always test with a small dataset first
- Consider running in dry-run mode before actual execution
- The script maintains backward compatibility with existing `mongo-db.py` functions
