import { Command } from 'commander';
import { migrateFacilities } from '../modules/facility/facility.migration';

export function createFacilityCommand(): Command {
  const facilityCommand = new Command('facility');
  
  facilityCommand
    .description('Migrate facility data from CSV to MongoDB')
    .option('-d, --database <name>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateFacilities(options.database);
      } catch (error) {
        console.error('Error executing facility migration command:', error);
        process.exit(1);
      }
    });
  
  return facilityCommand;
}