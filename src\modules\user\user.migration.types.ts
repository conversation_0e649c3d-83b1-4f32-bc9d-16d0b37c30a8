import { Types } from 'mongoose';
import { ENUM_GENDER, ENUM_RELATION } from '../../common/enums/enums';

/**
 * Interface for CSV user data
 */
export interface ICsvUser {
  id: string;
  firstName: string;
  lastName: string;
  countryCode?: string;
  mobile: string;
  email: string;
  relation?: ENUM_RELATION;
  dob?: string;
  gender?: ENUM_GENDER;
  role: string;
  facilityId?: string;
  age?: number;
  isActive?: boolean;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  photo?: string;
  emergencyContactPerson?: string;
  emergencyContactPhone?: string;
  createdAt?: string;
}

/**
 * Interface for migration batch configuration
 */
export interface IBatchConfig {
  batchSize: number;
  maxRetries: number;
  retryDelayMs: number;
  logProgressInterval: number;
  transactionTimeoutMs: number;
}

/**
 * Interface for individual user migration result
 */
export interface IUserMigrationResult {
  rowNumber: number;
  csvData: ICsvUser;
  success: boolean;
  userId?: Types.ObjectId;
  staffId?: Types.ObjectId;
  clientId?: Types.ObjectId;
  errorMessage?: string;
  errorType?: 'VALIDATION' | 'DATABASE' | 'DUPLICATE' | 'FACILITY' | 'ROLE' | 'UNKNOWN';
  processingTime?: number;
  processedAt: Date;
}

/**
 * Interface for batch processing result
 */
export interface IBatchResult {
  batchNumber: number;
  batchSize: number;
  startTime: Date;
  endTime: Date;
  processingTime: number;
  successCount: number;
  errorCount: number;
  results: IUserMigrationResult[];
}

/**
 * Interface for overall migration statistics
 */
export interface IMigrationStats {
  totalRecords: number;
  totalBatches: number;
  totalProcessed: number;
  totalSuccessful: number;
  totalFailed: number;
  totalUsers: number;
  totalStaff: number;
  totalClients: number;
  validationErrors: number;
  duplicateErrors: number;
  databaseErrors: number;
  facilityErrors: number;
  roleErrors: number;
  unknownErrors: number;
  startTime: Date;
  endTime?: Date;
  totalProcessingTime?: number;
  averageBatchTime?: number;
  recordsPerSecond?: number;
}

/**
 * Interface for successful migration record (for CSV output)
 */
export interface ISuccessfulMigrationRecord {
  rowNumber: number;
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  role: string;
  facilityId?: string;
  userId: string;
  staffId?: string;
  clientId?: string;
  processingTime: number;
  processedAt: string;
  batchNumber: number;
}

/**
 * Interface for failed migration record (for CSV output)
 */
export interface IFailedMigrationRecord {
  rowNumber: number;
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  role: string;
  facilityId?: string;
  errorType: string;
  errorMessage: string;
  processingTime: number;
  processedAt: string;
  batchNumber: number;
}

/**
 * Interface for migration progress tracking
 */
export interface IMigrationProgress {
  currentBatch: number;
  totalBatches: number;
  currentRecord: number;
  totalRecords: number;
  successfulRecords: number;
  failedRecords: number;
  percentComplete: number;
  estimatedTimeRemaining?: number;
  averageRecordsPerSecond?: number;
}

/**
 * Interface for batch processing context
 */
export interface IBatchContext {
  batchNumber: number;
  startIndex: number;
  endIndex: number;
  batchData: Array<{ data: ICsvUser; rowNumber: number }>;
  session: any;
  stats: IMigrationStats;
  config: IBatchConfig;
}

/**
 * Enum for migration phases
 */
export enum MigrationPhase {
  INITIALIZATION = 'INITIALIZATION',
  DATA_LOADING = 'DATA_LOADING',
  VALIDATION = 'VALIDATION',
  BATCH_PROCESSING = 'BATCH_PROCESSING',
  RESULT_GENERATION = 'RESULT_GENERATION',
  CLEANUP = 'CLEANUP',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

/**
 * Interface for migration status
 */
export interface IMigrationStatus {
  phase: MigrationPhase;
  message: string;
  progress?: IMigrationProgress;
  timestamp: Date;
}

/**
 * Default batch configuration
 */
export const DEFAULT_BATCH_CONFIG: IBatchConfig = {
  batchSize: 50, // Reduced batch size to avoid transaction timeouts
  maxRetries: 3,
  retryDelayMs: 1000,
  logProgressInterval: 5, // Log progress every 5 batches
  transactionTimeoutMs: 30000 // 30 second timeout per batch transaction
};

/**
 * CSV headers for successful migrations
 */
export const SUCCESSFUL_MIGRATION_HEADERS = [
  'rowNumber',
  'id',
  'firstName',
  'lastName',
  'email',
  'mobile',
  'role',
  'facilityId',
  'userId',
  'staffId',
  'clientId',
  'processingTime',
  'processedAt',
  'batchNumber'
];

/**
 * CSV headers for failed migrations
 */
export const FAILED_MIGRATION_HEADERS = [
  'rowNumber',
  'id',
  'firstName',
  'lastName',
  'email',
  'mobile',
  'role',
  'facilityId',
  'errorType',
  'errorMessage',
  'processingTime',
  'processedAt',
  'batchNumber'
];
