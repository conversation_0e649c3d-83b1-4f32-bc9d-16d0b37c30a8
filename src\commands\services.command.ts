import { Command } from 'commander';
import { migrateServices } from '../modules/pricing/service-appointment-type.migration';

export function createAppointmentTypeCommand(): Command {
  const appointmentTypeCommand = new Command('service');

  appointmentTypeCommand
    .description('Migrate appointment type (service) data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateServices(options.database);
      } catch (error) {
        console.error('Error executing appointment type migration command:', error);
        process.exit(1);
      }
    });

  return appointmentTypeCommand;
}
