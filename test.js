const { MongoClient } = require('mongodb');
const { Types } = require('mongoose');

// --- 1. Unique ID Generation Function (Max 10 Chars, Alphanumeric) ---
/**
 * Generates a pseudo-unique, exactly 10-character alphanumeric string (0-9, a-z, A-Z).
 * It leverages the cryptographically secure random number generator for maximum entropy.
 *
 * @returns {string} Exactly 10 characters long.
 */
function generateUnique10CharID() {
    // 1. Define the full character pool (62 characters: 0-9, a-z, A-Z)
    const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const charactersLength = characters.length;
    let result = '';

    // 2. Create a high-entropy seed using the crypto API for strong randomness.
    // We generate enough random bytes to fill the 10 slots very securely.
    const randomBytes = new Uint8Array(15); // Generate more than needed (15 bytes)

    // Check if crypto is available; if not, fall back to Math.random()
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        crypto.getRandomValues(randomBytes);
    } else {
        // Fallback for environments without crypto (less secure)
        for (let i = 0; i < randomBytes.length; i++) {
            randomBytes[i] = Math.floor(Math.random() * 256);
        }
    }

    // 3. Build the 10-character string by mapping random bytes to the character set
    for (let i = 0; i < 10; i++) {
        // Use the modulo operator to map the random byte value (0-255) 
        // into an index within our 62-character set.
        const index = randomBytes[i] % charactersLength;
        result += characters.charAt(index);
    }

    return result;
}
// ----------------------------------------------------------------------


// --- 2. Database Connection and Update Logic ---

// **⚠️ IMPORTANT: Replace with your actual connection details**
const uri = "";
const dbName = "test-hopwellness";

async function updateUsersWithoutClientId() {
    const client = new MongoClient(uri);

    try {
        await client.connect();
        console.log("✅ Successfully connected to MongoDB.");

        const db = client.db(dbName);
        const clientCollection = db.collection('clients');
        const userCollection = db.collection('users');

        // 1. Find all users that do not have the 'clientId' field OR where it is null.
        const query = {
            // userId: new Types.ObjectId('69035497c4d5fa30f527f287')
            $or: [
                { clientId: { $exists: false } },
                { clientId: null },
                { clientId: "" }
            ]
        };

        const usersToUpdate = await clientCollection.find(query).toArray();
        console.log(`🔍 Found ${usersToUpdate.length} user(s) missing a clientId.`);

        if (usersToUpdate.length === 0) {
            console.log("👍 No users need updating. Exiting.");
            return;
        }

        let updateCount = 0;

        // 2. Iterate through the found users and update them individually
        let blkOperationsUser = [];
        let blkOperationsClient = [];
        for (const user of usersToUpdate) {
            const newClientId = generateUnique10CharID();

            // Generate a *new* unique ID and ensure it doesn't already exist in the collection
            // NOTE: For a production system, you'd add a loop here to check for uniqueness
            // before updating. For simplicity, we trust the highly random generator for now.

            blkOperationsUser.push({
                updateOne: {
                    filter: { _id: user.userId },
                    update: { $set: { 'profileDetails.clientId': `C-${newClientId}` } }
                }
            });
            blkOperationsClient.push({
                updateOne: {
                    filter: { userId: user.userId },
                    update: { $set: { clientId: `C-${newClientId}` } }
                }
            });

            if (blkOperationsUser.length >= 1000) {
                await userCollection.bulkWrite(blkOperationsUser);
                await clientCollection.bulkWrite(blkOperationsClient);
                blkOperationsUser = [];
                blkOperationsClient = [];
            }
            updateCount++;
            if (updateCount % 100 === 0) {
                console.log(`\n${updateCount} users processed...`);
            }
        }
        if (blkOperationsUser.length > 0) {
            await userCollection.bulkWrite(blkOperationsUser);
            await clientCollection.bulkWrite(blkOperationsClient);
            blkOperationsUser = [];
            blkOperationsClient = [];
        }
        console.log(`\n🎉 Script finished. Total users updated: ${updateCount}/${usersToUpdate.length}`);

    } catch (error) {
        console.error("❌ An error occurred during the update process:", error);
    } finally {
        await client.close();
        console.log("🛑 Connection closed.");
    }
}

updateUsersWithoutClientId();