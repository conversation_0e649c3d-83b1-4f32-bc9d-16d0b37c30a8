

from __future__ import annotations
import os
import csv
import argparse
from typing import List, Dict
from datetime import datetime
import pymysql
import re
import PyPDF2
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import pymongo
import os
import dotenv
from bson import ObjectId


dotenv.load_dotenv()



client = mongo_client = pymongo.MongoClient(os.getenv("MONGODB_URI"))
database = client[os.getenv("DATABASE_NAME")]

organizationId = ObjectId('688c6b606b0e4946b3267218')

pricing = database["pricings"]


csv_data = csv.DictReader(open('C:/Users/<USER>/Downloads/Star-booking-pricing-prod.csv', 'r'))

# update pricing name by its id
bulk_operations = []
for row in csv_data:
    if not row['name']:
        continue
    bulk_operations.append(pymongo.UpdateOne({'id': row['id'], 'organizationId': organizationId}, {'$set': {'name': row['name']}}))
    print(row['id'], row['name'])

pricing.bulk_write(bulk_operations)


