import { Schema, model, Document, Types } from 'mongoose';

// Define the TypeScript interface for type checking
export interface IMembership extends Document {
    id: string;
    name: string;
    prefix: string;
    isActive: boolean;
    organizationId: Types.ObjectId;
    counter: number;
    lastcounter?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

// Define the schema
const MembershipSchema = new Schema<IMembership>(
    {
        id: {
            type: String,
            required: false,
        },
        name: {
            type: String,
            required: true,
        },
        prefix: {
            type: String,
            required: true,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        organizationId: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true,
        },
        counter: {
            type: Number,
            required: true,
        },
        lastcounter: {
            type: Number,
            default: 0,
        },
    },
    {
        timestamps: true, // adds createdAt and updatedAt
    }
);

// Create and export the model
export const Membership = model<IMembership>('memberships', MembershipSchema);
