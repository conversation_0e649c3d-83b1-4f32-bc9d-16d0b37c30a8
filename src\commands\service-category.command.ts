import { Command } from 'commander';
import { migrateServicesCategory } from '../modules/pricing/service-category.migration';

export function serviceCategoryCommand(): Command {
  const serviceCategoryCommand = new Command('service-category');

  serviceCategoryCommand
    .description('Migrate service category data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateServicesCategory(options.database);
      } catch (error) {
        console.error('Error executing service category migration command:', error);
        process.exit(1);
      }
    });

  return serviceCategoryCommand;
}
