# Session Deduction Script

This script deducts sessions from user purchases based on a CSV file containing desired remaining session counts.

## Features

- **Total Session Tracking**: Calculates total sessions available in the system for each user
- **Smart Session Deduction**: Only deducts sessions when current available sessions exceed the desired remaining sessions
- **Safety Validation**: Prevents deduction when current available sessions are less than required remaining sessions
- **Dry Run Mode**: Preview changes without executing them
- **Comprehensive Reporting**: Generates detailed CSV reports with session tracking information

## Usage

### Basic Usage

```bash
python deduct-sessions.py --csv remaining-sessions.csv --org-id YOUR_ORG_ID
```

### Dry Run (Preview Only)

```bash
python deduct-sessions.py --csv remaining-sessions.csv --org-id YOUR_ORG_ID --dry-run
```

### Verbose Mode (Detailed Session Breakdown)

```bash
python deduct-sessions.py --csv remaining-sessions.csv --org-id YOUR_ORG_ID --verbose
```

## CSV Format

The input CSV file should contain the following columns:

- `user id`: User ID (ObjectId or custom ID)
- `name`: User name
- `remaining sessions`: Desired remaining session count

Example:

```csv
user id,name,remaining sessions
507f1f77bcf86cd799439011,John Doe,5
507f1f77bcf86cd799439012,Jane Smith,10
```

## Logic

1. **Calculate Total Sessions**: For each user, calculates total sessions purchased in the system
2. **Calculate Current Available Sessions**: Determines currently available sessions from ACTIVE, NON-EXPIRED purchases only
   - Formula: Sum of (totalSessions - sessionConsumed) for each active purchase
   - Only includes purchases where `isActive = true` AND `isExpired = false`
3. **Determine Deduction**: Only deducts sessions if current available > desired remaining
4. **Safety Check**: Prevents deduction if current available < desired remaining (flags as adjustment needed)
5. **Session Deduction**: Deducts from oldest purchases first (FIFO)

## Output Files

The script generates three CSV files:

### 1. Errors (`remaining-sessions-errors.csv`)

Contains validation errors and users with insufficient sessions.

### 2. Success (`remaining-sessions-success.csv`)

Contains successful session deductions with purchase details.

### 3. Final Report (`remaining-sessions-final.csv`)

Comprehensive report with columns:

- Index
- User ID
- Name
- CSV Remaining Sessions (from input)
- Total Sessions in System
- Current Available Sessions (from active, non-expired purchases only)
- Active Purchases (count of active purchases)
- Expired Purchases (count of expired purchases)
- Sessions to Deduct
- Consumed Sessions (actually deducted)
- Purchase IDs (affected purchases)
- Adjustment Needed (Yes/No)

## Safety Features

- **Validation**: Checks for missing user IDs, names, and negative session counts
- **Insufficient Sessions Protection**: Won't deduct if user doesn't have enough sessions
- **Dry Run Mode**: Preview changes before execution
- **Detailed Logging**: Shows exactly what will happen for each user
- **Error Reporting**: Comprehensive error tracking and reporting

## Configuration

The script uses configuration from `config.py`:

- `MONGO_CONFIG`: MongoDB connection settings
- `DATA_FOLDER`: Directory for input/output files
- `ORGANIZATION_ID`: Default organization ID

## Environment Variables

Required environment variables (in `.env` file):

```env
MONGODB_URI=mongodb://localhost:27017
DATABASE_NAME=your_database_name
ORGANIZATION_ID=your_organization_id
DATA_FOLDER=data
```

## Examples

### Example 1: User has sufficient sessions

- User has 20 total sessions, 15 available (5 consumed)
- CSV specifies 10 remaining sessions
- Script deducts 5 sessions (15 - 10 = 5)
- Result: 10 sessions remaining

### Example 2: User has insufficient sessions

- User has 10 total sessions, 8 available (2 consumed)
- CSV specifies 10 remaining sessions
- Script flags as "Adjustment Needed" and skips deduction
- Result: No changes made, error logged

### Example 3: User already has correct sessions

- User has 15 total sessions, 10 available (5 consumed)
- CSV specifies 10 remaining sessions
- Script deducts 0 sessions (10 - 10 = 0)
- Result: No changes needed
