from __future__ import annotations
import csv
import argparse
from typing import List, Type, TypeVar
from typing import Dict

class CsvModel:
    CSV_COLUMN_MAP: Dict[str, str] = {}

    def __init__(self, row_data: Dict[str, str], index: int = -1):
        self.index = index
        self.load_from_row(row_data)

    def load_from_row(self, row_data: Dict[str, str]):
        """Assigns values to attributes based on CSV_COLUMN_MAP"""
        for attr, col_name in self.CSV_COLUMN_MAP.items():
            setattr(self, attr, row_data.get(col_name, "").strip())
    
    def __repr__(self):
        tmp = ''
        for key, value in self.__dict__.items():
            if key == 'index' and self.index == -1:
                continue
            tmp += f"{key}={value}, "
        return f"CsvOrder({tmp[:-2]})"


T = TypeVar("T", bound=CsvModel)

def csv_parser(cls: Type[T], file_path: str) -> List[T]:
    data: List[T] = []
    csv_headers = cls.CSV_COLUMN_MAP.values()
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file, skipinitialspace=True)
        for index, row in enumerate(reader):
            row_data = {k: row.get(k, '').strip() for k in csv_headers}
            obj: T = cls(row_data, index)
            data.append(obj)
    
    return data