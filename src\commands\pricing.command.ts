import { Command } from 'commander';
import { migratePricing } from '../modules/pricing/pricing.migration';

export function createPricingCommand(): Command {
  const pricingCommand = new Command('pricing');

  pricingCommand
    .description('Migrate pricing data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .option('-m, --mode <mode>', 'Migration mode: create, update, or upsert', 'create')
    .action(async (options) => {
      try {
        // Validate mode option
        const validModes = ['create', 'update', 'upsert'];
        if (!validModes.includes(options.mode)) {
          console.error(`Invalid mode: ${options.mode}. Valid modes are: ${validModes.join(', ')}`);
          process.exit(1);
        }

        await migratePricing(options.database, options.mode);
      } catch (error) {
        console.error('Error executing pricing migration command:', error);
        process.exit(1);
      }
    });

  return pricingCommand;
}
