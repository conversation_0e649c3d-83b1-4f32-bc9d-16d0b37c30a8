#!/usr/bin/env node
// import './types/global';
import { getCityMap } from './common/utils/city.map';
import { getStateMap } from './common/utils/state.map';
import { createCommands } from './commands';
import { config } from './common/config/config';
import { getRoleMap } from './common/utils/role.map';
import { getFacilityIdDocMap } from './modules/facility/facility.migration';
import { getOrganizationSetting, validateOrganization } from './modules/organization/organization.service';
import { connectToMongo } from './common/database/db.module';
import path from 'path';


async function bootStrap() {
  try {

    process.env.DATA_FOLDER = process.env.DATA_FOLDER || path.resolve(process.cwd(), 'data');

    global.config = config;
    global.roleMap = await getRoleMap();
    global.organization = await validateOrganization();
    global.organizationSetting = await getOrganizationSetting();
    global.cityMap = await getCityMap();
    global.stateMap = await getStateMap();
    global.facilityMap = await getFacilityIdDocMap();

    // Create and run the command-line interface
    const program = createCommands();
    program.parse(process.argv);

    // If no arguments provided, show help
    if (process.argv.length <= 2) {
      program.help();
    }
  } catch (error) {
    console.error('Error bootstrapping global variables:', error);
    process.exit(1);
  }
}

bootStrap()