import * as path from 'path';
import * as crypto from "crypto";
import { Types } from "mongoose";
import { connectToMongo, closeConnection } from '../../common/database/db.module';
import { ICsvRowWithNumber, parseCSVWithRowNumbers } from '../../common/utils/csv-parser';
import { User, IUser as UserDocument } from './user.model';
import { Role, IRole as RoleDocument } from '../role/role.model';
import LoggerConfig from '../../common/logger/log.module';
import { Staff } from './staff.model';
import { Client } from './client.model';
import { ENUM_ROLE_TYPE } from '../role/role.enum';

import uuid4 from "uuid4";
import { IFacility } from '../facility/facility.model';
import { UserMigrationBatchProcessor } from './user.migration.batch';
import { MigrationLogger } from './user.migration.logger';
import { MigrationResultGenerator } from './user.migration.results';
import {
  ICsvUser,
  IBatchConfig,
  DEFAULT_BATCH_CONFIG,
  MigrationPhase
} from './user.migration.types';
import { ENUM_RELATION } from '../../common/enums/enums';



const CsvToObjectKeyMapUser: Record<keyof ICsvUser, string> = {
  id: "id",
  firstName: "first name",
  lastName: "last name",
  countryCode: "country code",
  mobile: "mobile",
  email: "email",
  relation: "relation",
  dob: "dob",
  gender: "gender",
  role: "role",
  facilityId: "facility id",
  age: "age",
  isActive: "is active",
  address1: "address 1",
  address2: "address 2",
  city: "city",
  state: "state",
  postalCode: "postal code",
  country: "country",
  photo: "photo",
  emergencyContactPerson: "emergency contact person",
  emergencyContactPhone: "emergency contact phone",
  createdAt: "created at",
}

const logger = LoggerConfig('user.migration');
const duplicateClientsIds = new Set<string>();

/**
 * Migrate user data from CSV to MongoDB using batch processing
 */
export async function migrateUsers(
  _dbName: string = 'hop-migration',
  batchConfig: IBatchConfig = DEFAULT_BATCH_CONFIG
): Promise<void> {
  const migrationLogger = new MigrationLogger();

  try {
    // Initialize migration
    migrationLogger.setPhase(MigrationPhase.INITIALIZATION, 'Setting up migration environment');

    // Connect to database
    await connectToMongo();

    // Load and parse CSV data
    migrationLogger.setPhase(MigrationPhase.DATA_LOADING, 'Loading CSV data into memory');
    const userDataWithRows = await parseCSVWithRowNumbers<ICsvUser>('users.csv', CsvToObjectKeyMapUser);
    // const userDataWithRows = await parseCSVWithRowNumbers<ICsvUser>('staffs-CC.csv', CsvToObjectKeyMapUser);

    if (!userDataWithRows || userDataWithRows.length === 0) {
      logger.info('No user data found in CSV file');
      return;
    }

    // Log migration start with configuration
    migrationLogger.logMigrationStart(userDataWithRows.length, batchConfig.batchSize);

    // Check for duplicate users in database (without transaction)
    migrationLogger.setPhase(MigrationPhase.VALIDATION, 'Validating data and checking for duplicates');
    const filteredData: ICsvRowWithNumber<ICsvUser>[] = await checkForDuplicateUsers(userDataWithRows);

    // Process users in batches with individual transactions
    migrationLogger.setPhase(MigrationPhase.BATCH_PROCESSING, 'Processing users in batches');
    const batchProcessor = new UserMigrationBatchProcessor(batchConfig);
    const { stats, results } = await batchProcessor.processBatches(filteredData);

    // Generate result files
    migrationLogger.setPhase(MigrationPhase.RESULT_GENERATION, 'Generating result CSV files');
    const csvFiles = await MigrationResultGenerator.generateResultFiles(results, stats);

    // Generate error report if there are failures
    if (stats.totalFailed > 0) {
      await MigrationResultGenerator.generateErrorReport(results);
    }

    // Log completion
    migrationLogger.setPhase(MigrationPhase.COMPLETED, 'Migration completed successfully');
    migrationLogger.logMigrationComplete(stats, csvFiles);

  } catch (error) {
    logger.error('Error in migration process:', error);

    // Log migration failure
    migrationLogger.logMigrationFailure(error instanceof Error ? error : new Error(String(error)));
    throw error;

  } finally {
    // Cleanup
    migrationLogger.setPhase(MigrationPhase.CLEANUP, 'Cleaning up resources');
    await closeConnection();
  }
}

/**
 * Check for duplicate users in the database
 */
async function checkForDuplicateUsers(
  userDataWithRows: ICsvRowWithNumber<ICsvUser>[]
): Promise<ICsvRowWithNumber<ICsvUser>[]> {
  const id = new Set<string>();
  const usernames = userDataWithRows
    .map(({ data }) => {
      id.add(data.id?.toString()?.trim());
      return (data.email || data.mobile)
    })
    .filter(Boolean);

  if (usernames.length === 0 || id.size === 0) {
    return userDataWithRows;
  }

  try {
    const existingUsers = await User.find({
      organizationId: new Types.ObjectId(global.config.organizationId),
      $or: [
        {
          $and: [
            {
              $or: [
                { email: { $in: usernames } },
                { mobile: { $in: usernames } }
              ]
            },
            {
              $or: [
                { parent: null },
                { parent: { $exists: false } }
              ]
            },
          ]
        },
        { id: { $nin: Array.from(id) } }
      ]

    }, { email: 1, mobile: 1, id: 1 }).exec();

    // if (existingUsers.length > 0) {
    //   const duplicateIdentifiers = existingUsers.map((user: any) =>
    //     `(${user.email || ''} ${user.mobile ? ', ' + user.mobile : ''})`
    //   );
    //   const errorMessage = `Duplicate users found in database: ${duplicateIdentifiers.join(', ')}`;
    //   logger.error(errorMessage);
    //   throw new Error(errorMessage);
    // }

    // Filter out existing users from the input data
    if (existingUsers.length > 0) {
      const existingIdentifiers = new Set<string>();
      existingUsers.forEach((user: any) => {
        if (user.email) existingIdentifiers.add(user.email);
        if (user.mobile) existingIdentifiers.add(user.mobile);
        if (user.id) existingIdentifiers.add(user.id);
      });

      // Remove existing users from the array
      const originalLength = userDataWithRows.length;
      for (let i = userDataWithRows.length - 1; i >= 0; i--) {
        const { data } = userDataWithRows[i];
        // if ((data.id && existingIdentifiers.has(data.id)) || (data.email && existingIdentifiers.has(data.email)) || (data.mobile && existingIdentifiers.has(data.mobile))) {
        if ((data.id && existingIdentifiers.has(data.id))) {
          userDataWithRows.splice(i, 1);
        }
      }

      const removedCount = originalLength - userDataWithRows.length;
      logger.info(`Filtered out ${removedCount} existing users. Processing ${userDataWithRows.length} new users.`);
    }
    return userDataWithRows;
  } catch (error) {
    logger.error('Error checking for duplicate users:', error);
    throw error;
  }
}



export async function getStaff(user: any, csvUser: ICsvUser, organization: string, facilityId: string): Promise<any> {
  const facilityIds = facilityId ? [new Types.ObjectId(facilityId)] : [];

  const address = await getStaffAddress(csvUser.city, csvUser.state, csvUser.address1, csvUser.postalCode, csvUser.country);

  let dob = new Date('1970-01-01T00:00:00.000Z');
  if (csvUser.dob) {
    try {
      dob = new Date(csvUser.dob);
    } catch (error) { logger.error('Error parsing dob:', error); }
  }
  const staff = new Staff({
    id: user.id,
    createdBy: new Types.ObjectId(organization),
    userId: user._id,
    organizationId: new Types.ObjectId(organization),
    facilityId: facilityIds,
    address,
    gender: csvUser.gender?.toLocaleLowerCase(),
    dateOfBirth: dob,
    setUpDate: csvUser.createdAt ? new Date(csvUser.createdAt) : null,
    createdAt: csvUser.createdAt ? new Date(csvUser.createdAt) : new Date(),
  });
  const hash = crypto.createHash('sha256').update(uuid4() + (user.email || user.mobile) + staff._id.toString()).digest('hex');
  const randomId = hash.substring(0, 7);
  staff.staffId = `S-${randomId}`;
  return staff;
}

function generateUnique10CharID() {
  // 1. Define the full character pool (62 characters: 0-9, a-z, A-Z)
  const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const charactersLength = characters.length;
  let result = '';

  // 2. Create a high-entropy seed using the crypto API for strong randomness.
  // We generate enough random bytes to fill the 10 slots very securely.
  const randomBytes = new Uint8Array(15); // Generate more than needed (15 bytes)

  // Check if crypto is available; if not, fall back to Math.random()
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    crypto.getRandomValues(randomBytes);
  } else {
    // Fallback for environments without crypto (less secure)
    for (let i = 0; i < randomBytes.length; i++) {
      randomBytes[i] = Math.floor(Math.random() * 256);
    }
  }

  // 3. Build the 10-character string by mapping random bytes to the character set
  for (let i = 0; i < 10; i++) {
    // Use the modulo operator to map the random byte value (0-255) 
    // into an index within our 62-character set.
    const index = randomBytes[i] % charactersLength;
    result += characters.charAt(index);
  }

  return result;
}

export async function getClient(user: any, csvUser: ICsvUser, organization: string, facilityId: Types.ObjectId): Promise<any> {
  const address = getClientAddress(csvUser.address1, csvUser.address2, csvUser.city, csvUser.state, csvUser.postalCode, csvUser.country);
  let dob = new Date('1970-01-01T00:00:00.000Z');
  if (csvUser.dob) {
    try {
      dob = new Date(csvUser.dob);
    } catch (error) { logger.error('Error parsing dob:', error); }
  }
  const client = new Client({
    id: user.id,
    createdBy: new Types.ObjectId(organization),
    userId: user._id,
    organizationId: new Types.ObjectId(organization),
    facilityId: new Types.ObjectId(facilityId),
    address,
    dob: dob,
    gender: csvUser?.gender.toLowerCase() || undefined,
    relation: csvUser.relation ? ENUM_RELATION.CHILD : undefined,
    photo: csvUser.photo,
    emergencyContactPerson: csvUser.emergencyContactPerson,
    emergencyContactPhone: csvUser.emergencyContactPhone,
    createdAt: csvUser.createdAt ? new Date(csvUser.createdAt) : new Date(),
  });

  client.clientId = `C-${generateUnique10CharID()}`;
  duplicateClientsIds.add(client.clientId);
  return client;
}

const getStaffAddress = async (city: string, state: string, street: string, postalCode: string, country: string) => {
  // Make sure the maps are initialized
  const stateId = global.stateMap.get(state?.toLowerCase());
  const cityId = global.cityMap.get(city?.toLowerCase());

  const address = {
    stateId,
    cityId,
    street,
    postalCode,
    country,
  };

  if (!address.stateId) {
    logger.warn(`State not found for "${state}". Using default state.`);
    // Instead of throwing an error, we could use a default state ID or null
    address.stateId = null;
  }

  if (!address.cityId) {
    logger.warn(`City not found for "${city}". Using default city.`);
    // Instead of throwing an error, we could use a default city ID or null
    address.cityId = null;
  }

  return address;
}

const getClientAddress = (addressLine1: string, addressLine2: string, city: string, state: string, postalCode: string, country: string) => {

  if (!state) {
    throw new Error(`State is required for client address`);
  }

  const address = {
    addressLine1: addressLine1,
    addressLine2: addressLine2,
    city: city ? global.cityMap.get(city?.toLowerCase()) : null,
    state: state ? global.stateMap.get(state?.toLowerCase()) : null,
    postalCode: postalCode && !isNaN(Number(postalCode)) ? Number(postalCode) : undefined,
    country: country ? country : "",
    isDefault: false
  };

  return address;
}