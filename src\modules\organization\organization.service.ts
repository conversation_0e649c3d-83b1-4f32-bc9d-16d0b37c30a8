
import { getCollection } from "../../common/database/db.module";
import { ENUM_ROLE_TYPE } from "../role/role.enum";
import { User } from "../user/user.model";
import { Organization } from "./organization.model";

export const getOrganization = async (organizationId: string) => {
  const user = await User.find({ _id: organizationId, role: global.roleMap.get(ENUM_ROLE_TYPE.ORGANIZATION) });
  return user
}

export const validateOrganization = async () => {
  try {
    if (!global.config.organizationId) {
      throw new Error('Organization ID is required');
    }
    const organization = await getOrganization(global.config.organizationId)
    if (!organization || organization.length === 0) {
      throw new Error('Organization not found');
    }
    return organization[0];
  } catch (error) {
    console.error('Error validating organization:', error);
    process.exit(10)
  }
}

export const getOrganizationSetting = async () => {
  try {
    const organizationSetting = Organization.findOne({ userId: global.config.organizationId });
    return organizationSetting;
  } catch (error) {
    console.error('Error fetching organization setting:', error);
    process.exit(10)
  }
}