import mongoose from 'mongoose';
import { Schema, Types } from 'mongoose';
import { ENUM_GENDER, ENUM_RELATION } from '../../common/enums/enums';

export interface IPolicyItem {
    policyType?: string;
    documentUrl?: string;
    isEnabled?: boolean;
    expiryDate?: Date;
    date?: Date;
    required?: boolean;
    policyId?: string | Types.ObjectId;
}

export interface IProfileDetails extends Document {
    createdBy: Types.ObjectId;
    membershipId?: string;
    clientId?: string;
    dob?: Date;
    gender?: ENUM_GENDER;
    relation?: ENUM_RELATION;
    subUserType?: string;
    userType?: string;
    activityLevel?: string;
    address?: Record<string, any> | null;
    isBusiness?: boolean;
    emergencyContactPerson?: string;
    emergencyContactPhone?: string;
    photo?: string;
    policies?: IPolicyItem[];
    basicAssessments?: Record<string, any>;
    source?: string;
    sourceId?: string;
    notes?: string;
    businessAddress?: Record<string, any> | null;
}

// Sub-schema for policies
export const PolicyItemSchema = new Schema<IPolicyItem>({
    policyType: String,
    documentUrl: { type: String, default: '' },
    isEnabled: { type: Boolean, default: true },
    expiryDate: Date,
    date: Date,
    required: { type: Boolean, default: false },
    policyId: { type: Schema.Types.ObjectId, ref: 'Policies' },
}, { _id: false, timestamps: false });

// Main ProfileDetails schema
export const ProfileDetailsSchema = new Schema({
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    membershipId: { type: String, default: null, required: false },
    clientId: String,
    dob: Date,
    gender: { type: String, enum: ENUM_GENDER },
    relation: { type: String, enum: ENUM_RELATION },
    subUserType: String,
    userType: String,
    activityLevel: String,
    address: { type: Object, default: null },
    isBusiness: { type: Boolean, default: false },
    emergencyContactPerson: String,
    emergencyContactPhone: String,
    photo: { type: String, default: '' },
    policies: { type: [PolicyItemSchema], default: [] },
    basicAssessments: { type: Object, default: {} },
    source: String,
    sourceId: { type: String, index: true },
    notes: { type: String, default: '' },
    businessAddress: {
        type: Object,
        default: null,
        validate: {
            validator: function (value) {
                return !this.isBusiness || (value && Object.keys(value).length > 0);
            },
            message: 'Business address is required when isBusiness is true.',
        }
    }
}, { versionKey: false, timestamps: false, _id: false });


module.exports = mongoose.model('ProfileDetails', ProfileDetailsSchema);

