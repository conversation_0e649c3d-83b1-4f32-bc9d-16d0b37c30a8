import mongoose, { Document, Schema, Types } from 'mongoose';
import { ENUM_PAYMENT_METHOD } from '../../common/enums/enums';

export interface IPaymentMethod extends Document {
    name: string;
    shortId: ENUM_PAYMENT_METHOD;
    imageUrl?: string;
    isActive: boolean;
    isDeleted: boolean;
    isDefault: boolean;
    createdBy: Types.ObjectId;
    createdAt: Date;
    updatedAt: Date;
}

const PaymentMethodSchema = new Schema<IPaymentMethod>({
    name: {
        type: String,
        required: true
    },
    shortId: {
        type: String,
        enum: ENUM_PAYMENT_METHOD,
        required: true
    },
    imageUrl: {
        type: String,
        required: false,
        trim: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isDefault: {
        type: Boolean,
        default: false
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: "User"
    }
}, { timestamps: true });

export const PaymentMethod = mongoose.model<IPaymentMethod>('PaymentMethod', PaymentMethodSchema);