import mongoose, { Document, Schema, Types } from 'mongoose';

/**
 * Status of a migration
 */
export enum MigrationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ROLLED_BACK = 'rolled_back'
}

/**
 * Type of migration
 */
export enum MigrationType {
  USER = 'user',
  FACILITY = 'facility',
  SERVICE = 'service',
  APPOINTMENT_TYPE = 'appointment_type',
  PRICING = 'pricing',
  ALL = 'all'
}

/**
 * Interface for migration details
 */
export interface IMigrationDetail {
  module: string;
  recordsProcessed: number;
  recordsSucceeded: number;
  recordsFailed: number;
  errors?: string[];
}

/**
 * Interface for migration tracker document
 */
export interface IMigrationTracker extends Document {
  organizationId: Types.ObjectId;
  migrationType: MigrationType;
  status: MigrationStatus;
  startTime: Date;
  endTime?: Date;
  details: IMigrationDetail[];
  createdBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for migration details
 */
const MigrationDetailSchema = new Schema<IMigrationDetail>({
  module: { 
    type: String, 
    required: true 
  },
  recordsProcessed: { 
    type: Number, 
    required: true,
    default: 0
  },
  recordsSucceeded: { 
    type: Number, 
    required: true,
    default: 0
  },
  recordsFailed: { 
    type: Number, 
    required: true,
    default: 0
  },
  errors: { 
    type: [String], 
    required: false 
  }
}, { _id: false });

/**
 * Schema for migration tracker
 */
const MigrationTrackerSchema = new Schema<IMigrationTracker>({
  organizationId: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "Organization" 
  },
  migrationType: { 
    type: String, 
    required: true,
    enum: Object.values(MigrationType)
  },
  status: { 
    type: String, 
    required: true,
    enum: Object.values(MigrationStatus),
    default: MigrationStatus.PENDING
  },
  startTime: { 
    type: Date, 
    required: true,
    default: Date.now
  },
  endTime: { 
    type: Date, 
    required: false 
  },
  details: { 
    type: [MigrationDetailSchema], 
    required: true,
    default: []
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "User" 
  }
}, { 
  timestamps: true 
});

// Create and export the model
export const MIGRATION_TRACKER_COLLECTION = 'migrationTrackers';
export const MigrationTracker = mongoose.model<IMigrationTracker>('MigrationTracker', MigrationTrackerSchema, MIGRATION_TRACKER_COLLECTION);
