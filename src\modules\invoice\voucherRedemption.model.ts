import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IVoucherRedemption extends Document {
    purchaseId: Types.ObjectId;
    userId: Types.ObjectId;
    organizationId: Types.ObjectId;
    usedVoucherAmount: number;
    remainingVoucherAmount: number;
    previousVoucherBalance: number;
    invoiceId: Types.ObjectId;
    createdBy?: Types.ObjectId;
    createdAt?: Date;
    updatedAt?: Date;
}

const VoucherRedemptionSchema = new Schema<IVoucherRedemption>(
    {
        purchaseId: {
            type: Schema.Types.ObjectId,
            ref: 'Purchase',
            required: true,
            index: true,
        },
        userId: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true,
        },
        organizationId: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true,
        },
        usedVoucherAmount: {
            type: Number,
            required: true,
            min: 0,
        },
        remainingVoucherAmount: {
            type: Number,
            required: true,
            min: 0,
        },
        previousVoucherBalance: {
            type: Number,
            required: true,
            min: 0,
        },
        invoiceId: {
            type: Schema.Types.ObjectId,
            ref: 'invoices',
            required: true,
        },
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
    },
    { timestamps: true }
);

// Compound indexes for efficient querying
VoucherRedemptionSchema.index({ purchaseId: 1, redemptionDate: -1 });

export const VoucherRedemption = mongoose.model<IVoucherRedemption>(
    'VoucherRedemption',
    VoucherRedemptionSchema
);
