import { Command } from 'commander';
import { migrateVoucher } from '../modules/pricing/voucher.migration';

export function createVoucherCommand(): Command {
  const pricingCommand = new Command('voucher');

  pricingCommand
    .description('Migrate voucher data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateVoucher(options.database);
      } catch (error) {
        console.error('Error executing voucher migration command:', error);
        process.exit(1);
      }
    });

  return pricingCommand;
}
