from __future__ import annotations
import os
import csv
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import pymongo
from pymongo import UpdateOne
from pymongo import MongoClient
from tqdm import tqdm
import dotenv
from bson import ObjectId
import math

from config import *

# CSV column mapping - easily changeable
CSV_COLUMN_MAP = {
    'user_id': 'user id',
    'name': '\ufeffname', 
    # 'name': 'name', 
    'remaining_sessions': 'remaining sessions'
}

class CsvSessionDeduction:
    """Class to represent a row from the CSV file"""

    def __init__(self, row_data: Dict[str, str], index: int):
        self.index = index
        self.user_id = row_data.get('user_id', '').strip()
        self.name = row_data.get('name', '').strip()
        self.remaining_sessions = int(row_data.get('remaining_sessions', '0') or '0')
        self.consumed_sessions = 0
        self.purchasesIds = []
        # New fields for tracking total sessions
        self.total_sessions_in_system = 0
        self.current_available_sessions = 0
        self.sessions_to_deduct = 0
        self.adjustment_needed = False
        self.active_purchases_count = 0
        self.expired_purchases_count = 0

    def __repr__(self):
        return f"CsvSessionDeduction(user_id={self.user_id}, name={self.name}, remaining_sessions={self.remaining_sessions}, total_in_system={self.total_sessions_in_system})"


def parse_csv_with_mapping(file_path: str) -> List[CsvSessionDeduction]:
    """Parse CSV file and return list of CsvSessionDeduction objects"""
    session_data = []
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for index, row in enumerate(reader):
            name = row.get(CSV_COLUMN_MAP['name'], '').strip()
            remaining_sessions = row.get(CSV_COLUMN_MAP['remaining_sessions'], '0').strip()
            user_id = row.get(CSV_COLUMN_MAP['user_id'], '').strip()
            session_obj = CsvSessionDeduction({
                'user_id': user_id,
                'name': name,
                'remaining_sessions': remaining_sessions
            }, index)
            session_data.append(session_obj)
    
    return session_data


def connect_to_mongo() -> MongoClient:
    """Connect to MongoDB"""
    mongo_uri = MONGO_CONFIG['uri']
    client = MongoClient(mongo_uri)
    print(mongo_uri)
    return client


def validate_session_data(session_data: List[CsvSessionDeduction]) -> List[str]:
    """Validate session data and return list of validation errors"""
    validation_errors = []

    for i, session in enumerate(session_data):
        if not session.user_id:
            validation_errors.append(f"Row {i+1}: Missing user ID")
        if not session.name:
            validation_errors.append(f"Row {i+1}: Missing name")
        if session.remaining_sessions < 0:
            validation_errors.append(f"Row {i+1}: Negative remaining sessions ({session.remaining_sessions})")

    return validation_errors


def get_purchase_session_breakdown(purchases: List[Dict]) -> str:
    """Get detailed breakdown of sessions in purchases for debugging"""
    breakdown = []
    for purchase in purchases:
        total = purchase.get('totalSessions', 0)
        consumed = purchase.get('sessionConsumed', 0)
        available = max(0, total - consumed)
        is_active = purchase.get('isActive', True)
        is_expired = purchase.get('isExpired', False)
        session_type = purchase.get('sessionType', 'unknown')

        status = "ACTIVE" if is_active and not is_expired else "INACTIVE/EXPIRED"
        breakdown.append(f"  Purchase {purchase['_id']}: {session_type} - {available}/{total} available ({status})")

    return "\n".join(breakdown) if breakdown else "  No purchases found"

def bulk_fetch_users_and_purchases(session_data: List[CsvSessionDeduction], organization_id: str):
    """Bulk fetch users and their purchases from MongoDB"""
    client = connect_to_mongo()
    db = client[MONGO_CONFIG['database']]
    errors = [['Index', 'User ID', 'Name', 'Remaining Sessions', 'Error']]
    # Extract unique user IDs
    user_ids = list(set([item.user_id for item in session_data if item.user_id]))
    
    print(f"Fetching {len(user_ids)} unique users...")
    
    # Bulk fetch users
    users = list(db.users.find({
        'organizationId': ObjectId(organization_id),
        "$or": [
            {'_id': {'$in': [ObjectId(uid) for uid in user_ids if ObjectId.is_valid(uid)]}},
            {'id': {'$in': [uid for uid in user_ids if not ObjectId.is_valid(uid)]}}
        ]
    }))
    
    # Create user mapping
    user_map = {}
    for user in users:
        if 'id' in user:
            user_map[str(user['id'])] = user
        if '_id' in user and ObjectId.is_valid(user['_id']):
            user_map[ str(user['_id']) ] = user
    
    print(f"Found {len(users)} users in database")
    
    # User not found error log
    for _session in session_data:
        if _session.user_id not in user_map:
            errors.append([_session.index, _session.user_id, _session.name, _session.remaining_sessions, 'User not found'])
    
    # Get user ObjectIds for purchase lookup
    user_object_ids = [user['_id'] for user in users]
    
    # Bulk fetch purchases - include all session types that have sessions
    print("Fetching purchases...")
    purchases = list(db.purchases.find({
        'userId': {'$in': user_object_ids},
        'organizationId': ObjectId(organization_id),
        'itemType': 'service',
        'sessionType': {'$in': [ 'day_pass', 'multiple', 'once_a_day', 'single' ]},
    }).sort([('createdAt', 1)]))
    
    # Group purchases by userId
    purchases_by_user = {}
    for purchase in purchases:
        user_id = str(purchase['userId'])
        if user_id not in purchases_by_user:
            purchases_by_user[user_id] = []
        purchases_by_user[user_id].append(purchase)

    # Calculate total sessions for each user in session_data
    for session_record in session_data:
        if session_record.user_id in user_map:
            user_object_id = str(user_map[session_record.user_id]['_id'])
            user_purchases = purchases_by_user.get(user_object_id, [])

            # Calculate total sessions and current available sessions
            total_sessions = 0
            current_available = 0
            active_purchases = 0
            expired_purchases = 0

            for purchase in user_purchases:
                purchase_total = 0
                if purchase.get('sessionType') == 'unlimited':
                    purchase_total = math.inf
                if purchase.get('sessionType') == 'multiple':
                    purchase_total = purchase.get('totalSessions', 0)
                if purchase.get('sessionType') == 'day_pass':
                    purchase_total = purchase.get('dayPassLimit', 1)
                if purchase.get('sessionType') == 'single':
                    purchase_total = 1
                    
                purchase_consumed = 0 # purchase.get('sessionConsumed', 0)
                is_expired = purchase.get('isExpired', False)
                is_active = purchase.get('isActive', True)

                if purchase_total > 0:
                    total_sessions += purchase_total

                    # Only count sessions from active, non-expired purchases
                    available_in_purchase = max(0, purchase_total - purchase_consumed)
                    current_available += available_in_purchase
                    
                if is_active and not is_expired:
                    active_purchases += 1
                    
                if is_expired:
                    expired_purchases += 1


            session_record.total_sessions_in_system = total_sessions
            session_record.current_available_sessions = current_available
            session_record.active_purchases_count = active_purchases
            session_record.expired_purchases_count = expired_purchases

            # Calculate how many sessions need to be deducted
            if current_available > session_record.remaining_sessions:
                session_record.sessions_to_deduct = current_available - session_record.remaining_sessions
            else:
                session_record.sessions_to_deduct = 0
                if current_available < session_record.remaining_sessions:
                    session_record.adjustment_needed = True
                    errors.append([
                        session_record.index,
                        session_record.user_id,
                        session_record.name,
                        session_record.remaining_sessions,
                        f'Current available sessions ({current_available}) is less than required remaining sessions ({session_record.remaining_sessions})'
                    ])

    # Error user's purchase not found
    for _session in session_data:
        if _session.user_id in user_map and _session.user_id not in purchases_by_user:
            errors.append([_session.index, _session.user_id, _session.name, _session.remaining_sessions, 'No purchases found'])

    print(f"Found {len(purchases)} total purchases")

    return user_map, purchases_by_user, errors

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Deduct sessions from user purchases')
    parser.add_argument('--csv', required=False, default=f'{DATA_FOLDER}/remaining-sessions.csv', help='Path to CSV file in data folder')
    parser.add_argument('--org-id', required=False, default=ORGANIZATION_ID, help='Organization ID')
    parser.add_argument('--dry-run', action='store_true', default=False, help='Preview changes without executing them')
    parser.add_argument('--verbose', action='store_true', help='Show detailed session breakdown for each user')
    
    args = parser.parse_args()
    
    # Construct full path to CSV file
    csv_path = os.path.join(DATA_FOLDER, args.csv)
    
    errors = [['Index', 'User ID', 'Name', 'Remaining Sessions', 'Error']]
    success = [['Index', 'User ID', 'Name', 'Remaining Sessions', 'Purchases id', 'Success']]
    final = [['Index', 'User ID', 'Name', 'CSV Remaining Sessions', 'Total Sessions in System', 'Current Available Sessions', 'Active Purchases', 'Expired Purchases', 'Sessions to Deduct', 'Consumed Sessions', 'Purchases Ids', 'Adjustment Needed']]
    
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found at {csv_path}")
        return
     
    print(f"Reading CSV from: {csv_path}")
    
    # Parse CSV data
    session_data = parse_csv_with_mapping(csv_path)
    print(f"Loaded {len(session_data)} records from CSV")

    # Validate session data
    validation_errors = validate_session_data(session_data)
    if validation_errors:
        print("Validation errors found:")
        for error in validation_errors:
            print(f"  - {error}")
        print("Please fix the CSV file and try again.")
        return
    
    # Bulk fetch users and purchases
    user_map, purchases_by_user, errors = bulk_fetch_users_and_purchases(session_data, args.org_id)
    errors.extend(errors[1:])
    
    # Process each session deduction record
    bulk_operations = []
    index = 0
    for session_record in tqdm(session_data, desc="Processing session deductions"):
        index += 1
        user = user_map.get(session_record.user_id)
        error = [index, session_record.user_id, session_record.name, session_record.remaining_sessions,]
        if not user:
            err = (f"User not found: {session_record.user_id} ({session_record.name})")
            error.append(err)
            errors.append(error)
            continue
        
        user_purchases = purchases_by_user.get(str(user['_id']), [])

        # Skip if adjustment is needed (current available < required remaining)
        if session_record.adjustment_needed:
            print(f"  Skipping {session_record.name}: Current available sessions ({session_record.current_available_sessions}) < Required remaining sessions ({session_record.remaining_sessions})")
            continue

        # Skip if no sessions need to be deducted
        if session_record.sessions_to_deduct <= 0:
            print(f"  No sessions to deduct for {session_record.name}: Current available ({session_record.current_available_sessions}) <= Required remaining ({session_record.remaining_sessions})")
            continue

        print(f"User {session_record.name}: Total sessions: {session_record.total_sessions_in_system}, Current available: {session_record.current_available_sessions} (from {session_record.active_purchases_count} active purchases), Need to deduct: {session_record.sessions_to_deduct}")

        # Show detailed breakdown if verbose mode is enabled
        if args.verbose:
            print(f"  Detailed session breakdown for {session_record.name}:")
            print(get_purchase_session_breakdown(user_purchases))

        # Deduct sessions from purchases (oldest first)
        sessions_left_to_deduct = session_record.sessions_to_deduct

        for purchase in user_purchases:
            if sessions_left_to_deduct <= 0:
                break

            # Skip if purchase is already fully consumed
            current_consumed = purchase.get('sessionConsumed', 0)
            total_sessions = 0 
            
            if purchase.get('sessionType') == 'unlimited':
                total_sessions = math.inf
            if purchase.get('sessionType') == 'multiple':
                total_sessions = purchase.get('totalSessions', 0)
            if purchase.get('sessionType') == 'day_pass':
                total_sessions = purchase.get('dayPassLimit', 1)
            if purchase.get('sessionType') == 'single':
                total_sessions = 1
            
            available_in_purchase = total_sessions - current_consumed

            if available_in_purchase <= 0:
                continue

            # Calculate how many sessions to deduct from this purchase
            session_to_deduct = min(sessions_left_to_deduct, available_in_purchase)
            sessions_left_to_deduct -= session_to_deduct

            # Update purchase
            new_consumed = current_consumed + session_to_deduct
            is_expired = purchase.get('isExpired', False)

            bulk_operations.append(UpdateOne(
                {'_id': purchase['_id']},
                {'$set': {
                    'sessionConsumed': new_consumed,
                    'isExpired': True if new_consumed >= total_sessions else is_expired,
                }}
            ))

            session_record.purchasesIds.append(str(purchase['_id']))
            session_record.consumed_sessions += session_to_deduct
            print(f"  Deducted {session_to_deduct} sessions from purchase {purchase['_id']}, {sessions_left_to_deduct} sessions left to deduct")
            success.append([index, session_record.user_id, session_record.name, session_record.remaining_sessions, str(purchase['_id']), f"Deducted {session_to_deduct} sessions"])
            
            if bulk_operations and len(bulk_operations) >= 1000 and not args.dry_run:
                print(f"Executing {len(bulk_operations)} bulk operations...")
                client = connect_to_mongo()
                db = client[MONGO_CONFIG['database']]
                res = db.purchases.bulk_write(bulk_operations)
                print(f"Bulk write completed: {res.modified_count} purchases updated")
                bulk_operations = []
            
    # Execute bulk write
    if bulk_operations:
        if args.dry_run:
            print(f"DRY RUN: Would execute {len(bulk_operations)} bulk operations")
            print("No actual changes made to database")
        else:
            print(f"Executing {len(bulk_operations)} bulk operations...")
            client = connect_to_mongo()
            db = client[MONGO_CONFIG['database']]
            res = db.purchases.bulk_write(bulk_operations)
            print(f"Bulk write completed: {res.modified_count} purchases updated")
    else:
        print("No bulk operations to execute")
        
    # Write errors to CSV
    with open(f'{DATA_FOLDER}/remaining-sessions-errors.csv', 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(errors)
    
    # Write success to CSV
    with open(f'{DATA_FOLDER}/remaining-sessions-success.csv', 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(success)
        
    # Write final to CSV
    with open(f'{DATA_FOLDER}/remaining-sessions-final.csv', 'w', newline='', encoding='utf-8') as csvfile:
        for session_record in session_data:
            final.append([
                session_record.index,
                session_record.user_id,
                session_record.name,
                session_record.remaining_sessions,  # CSV remaining sessions
                session_record.total_sessions_in_system,  # Total sessions in system
                session_record.current_available_sessions,  # Current available sessions
                session_record.active_purchases_count,  # Active purchases count
                session_record.expired_purchases_count,  # Expired purchases count
                session_record.sessions_to_deduct,  # Sessions to deduct
                session_record.consumed_sessions,  # Actually consumed sessions
                ', '.join(session_record.purchasesIds),  # Purchase IDs
                'Yes' if session_record.adjustment_needed else 'No'  # Adjustment needed
            ])
        writer = csv.writer(csvfile)
        writer.writerows(final)

    # Print summary
    print("\n" + "="*60)
    print("SESSION DEDUCTION SUMMARY" + (" (DRY RUN)" if args.dry_run else ""))
    print("="*60)

    total_users = len(session_data)
    users_with_adjustments = sum(1 for s in session_data if s.adjustment_needed)
    users_processed = sum(1 for s in session_data if s.consumed_sessions > 0)
    total_sessions_deducted = sum(s.consumed_sessions for s in session_data)

    print(f"Total users in CSV: {total_users}")
    print(f"Users successfully processed: {users_processed}")
    print(f"Users requiring adjustment: {users_with_adjustments}")
    print(f"Total sessions {'would be ' if args.dry_run else ''}deducted: {total_sessions_deducted}")
    print(f"Bulk operations {'would be ' if args.dry_run else ''}executed: {len(bulk_operations)}")

    if users_with_adjustments > 0:
        print(f"\nWARNING: {users_with_adjustments} users have insufficient sessions in the system!")
        print("Check the errors CSV file for details.")

    if args.dry_run:
        print(f"\nThis was a DRY RUN - no actual changes were made to the database.")
        print(f"Remove the --dry-run flag to execute the changes.")

    print(f"\nNOTE: 'Current Available Sessions' represents the total sessions")
    print(f"currently available in the system for each user, calculated as:")
    print(f"  Sum of (totalSessions - sessionConsumed) for all ACTIVE, NON-EXPIRED purchases")

    print(f"\nOutput files generated:")
    print(f"  - Errors: {DATA_FOLDER}/remaining-sessions-errors.csv")
    print(f"  - Success: {DATA_FOLDER}/remaining-sessions-success.csv")
    print(f"  - Final Report: {DATA_FOLDER}/remaining-sessions-final.csv")
    print("\nDone!")

            
        
        
        

if __name__ == "__main__":
    main()
