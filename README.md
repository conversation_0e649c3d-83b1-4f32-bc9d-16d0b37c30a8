# HOP Migration Scripts

Migration scripts for HOP application data from CSV to MongoDB.

## Invoice Migration Modes

The invoice migration now supports three different modes to handle create and update operations:

### Available Modes

1. **create** - Only creates new invoices, skips existing ones
2. **update** - Only updates existing invoices, skips non-existing ones
3. **upsert** - Creates new invoices or updates existing ones (default: update)

### Usage

#### Command Line Interface

```bash
# Using specific modes
npm run migrate:invoice:create    # Create mode only
npm run migrate:invoice:update    # Update mode only (default)
npm run migrate:invoice:upsert    # Upsert mode

# Or using the CLI directly
npm run migrate invoice --mode create
npm run migrate invoice --mode update
npm run migrate invoice --mode upsert

# For migrate-all command
npm run migrate:all --mode upsert
```

#### Mode Behavior

- **Create Mode**:
  - Processes only invoices that don't exist in the system
  - Skips invoices that already exist
  - Uses `invoice.save()` method

- **Update Mode** (default):
  - Processes only invoices that already exist in the system
  - Skips invoices that don't exist
  - Uses `invoice.update()` method

- **Upsert Mode**:
  - Processes all invoices regardless of existence
  - Creates new invoices or updates existing ones
  - Uses `invoice.upsert()` method

### Migration Reports

The migration generates detailed reports including:

- Mode used for migration
- Total invoices processed
- Successful operations
- Failed operations
- Skipped operations
- Success rate and duration

Reports are saved in `data/migration-results/invoice-migration-results/` directory.

---

Reference: <https://egmz.medium.com/building-a-cli-with-node-js-in-2024-c278802a3ef5>
