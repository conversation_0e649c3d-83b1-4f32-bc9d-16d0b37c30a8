import { Command } from 'commander';
import { migrateUsers } from '../modules/user/user.migration';
// import * as global from '../common/global';

export function createUserCommand(): Command {
  const userCommand = new Command('user');
  
  userCommand
    .description('Migrate user data from CSV to MongoDB')
    .option('-d, --database <name>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateUsers(options.database);
      } catch (error) {
        console.error('Error executing user migration command:', error);
        process.exit(1);
      }
    });
  
  return userCommand;
}
