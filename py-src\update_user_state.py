
from __future__ import annotations
import os
import csv
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import pymongo
from pymongo import UpdateOne
from pymongo import MongoClient
from tqdm import tqdm
import dotenv
from bson import ObjectId

from config import *

CSV_COLUMN_MAP = {
    'user_id': 'id',
    'state': 'state'
}

class CsvUserState:
    """Class to represent a row from the CSV file"""
    
    def __init__(self, row_data: Dict[str, str], index: int):
        self.index = index
        self.user_id = row_data.get(CSV_COLUMN_MAP['user_id'], '').strip()
        self.state = row_data.get(CSV_COLUMN_MAP['state'], '').strip()
    
    def __repr__(self):
        return f"CsvUserState(user_id={self.user_id}, state={self.state})"

def parse_csv_with_mapping(file_path: str) -> List[CsvUserState]:
    """Parse CSV file and return list of CsvUserState objects"""
    user_data = []
    
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file, skipinitialspace=True)
        for index, row in enumerate(reader):
            
            user_obj = CsvUserState({
                "id": row.get('\ufeffid', '').strip(),
                "state": row.get(CSV_COLUMN_MAP['state'], '').strip()
                }, index)
            user_data.append(user_obj)
    
    return user_data

def connect_to_mongo() -> MongoClient:
    """Connect to MongoDB"""
    mongo_uri = MONGO_CONFIG['uri']
    client = MongoClient(mongo_uri)
    return client

def get_user_id(db, user_id: str) -> Optional[str]:
    """Get user ID from database"""
    users_collection = db['users']
    user = users_collection.find_one({'$or': [{'id': user_id}]}, {'_id': 1})
    return user

def get_state_id(db, state_name: str) -> Optional[str]:
    """Get state ID from database"""
    states_collection = db['states']
    state = states_collection.find_one({'name': {'$regex': f'^{state_name}$', '$options': 'i'}})
    return state



def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Update user state')
    parser.add_argument('--csv', required=False, default=f'{DATA_FOLDER}/user-state-update.csv', help='Path to CSV file in data folder')
    args = parser.parse_args()
    
    # Construct full path to CSV file
    csv_path = os.path.join(DATA_FOLDER, args.csv)
    
    # Parse CSV data
    user_data = parse_csv_with_mapping(csv_path)
    print(f"Loaded {len(user_data)} records from CSV")
    
    # Connect to MongoDB
    client = connect_to_mongo()
    db = client[MONGO_CONFIG['database']]
    print("Connected to MongoDB", MONGO_CONFIG['database'])
    
    # Process each user state record
    bulk_operations = []
    # for user_record in tqdm(user_data, desc="Processing user states"):
    for user_record in user_data:
        user = get_user_id(db, user_record.user_id)
        state = get_state_id(db, user_record.state)
        
        if not user:
            print(f"User not found for {user_record.user_id}")
            continue
        if not state:
            print(f"State not found for {user_record.state}")
            continue
        
        bulk_operations.append(UpdateOne(
            {'userId': user['_id']},
            {'$set': {
                'address.state': state['_id'],
                'address.stateName': state['name'],
            }}
        ))
    
    if bulk_operations:
        db.clients.bulk_write(bulk_operations)
    
    
    print("Done!")

if __name__ == "__main__":
    main()
