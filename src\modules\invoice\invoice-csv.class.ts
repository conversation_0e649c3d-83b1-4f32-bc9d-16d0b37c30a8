import { ENUM_DISCOUNT_TYPE, ENUM_PAYMENT_METHOD, ENUM_PAYMENT_STATUS, ENUM_PRODUCT_ITEM_TYPE } from "../../common/enums/enums";
import { ICsvInvoice } from "./invoice.interface";

export class CsvInvoice implements ICsvInvoice {
    index: number;

    private _invoiceId!: string;
    private _invoiceDate!: string;
    private _userId!: string;
    private _billingClientId?: string;
    private _facilityId!: string;
    private _price: number;
    private _packageName!: string;
    private _amountPaid!: string;
    private _cartDiscountType?: string;
    private _cartDiscountAmount?: string;
    private _paymentMethod!: string;
    private _paymentStatus!: string;
    private _invoiceItemId!: string;
    private _itemId!: string;
    private _itemType!: string;
    private _itemQuantity!: string;
    private _promotion?: string;
    private _itemDiscountType?: string;
    private _itemDiscountAmount?: string;
    private _startDate?: string;
    private _isBusiness?: string;
    private _employeeId?: string;
    private _voucherCode?: string;
    private _isReturnItem?: string;

    constructor(data: ICsvInvoice) {
        Object.assign(this, data);
    }
    // --- Typed Getters and Setters ---

    get invoiceId(): string {
        return this._invoiceId;
    }

    set invoiceId(val: string) {
        this._invoiceId = val;
    }

    get invoiceDate(): Date {
        return new Date(this._invoiceDate);
    }
    set invoiceDate(val: string | Date) {
        this._invoiceDate = typeof val === "string" ? val : val.toISOString();
    }

    get userId(): string {
        return this._userId;
    }
    set userId(val: string) {
        this._userId = val;
    }

    get billingClientId(): string | undefined {
        return this._billingClientId;
    }
    set billingClientId(val: string | undefined) {
        this._billingClientId = val;
    }

    get facilityId(): string {
        return this._facilityId;
    }
    set facilityId(val: string) {
        this._facilityId = val;
    }

    get amountPaid(): number {
        return parseFloat(this._amountPaid);
    }
    set amountPaid(val: number | string) {
        this._amountPaid = typeof val === "string" ? val : val.toString();
    }

    get price(): number {
        return this._price;
    }
    set price(val: number | string) {
        this._price = typeof val === "string" ? parseFloat(val) : val;
    }

    get packageName(): string {
        return this._packageName;
    }
    set packageName(val: string) {
        this._packageName = val;
    }

    get cartDiscountType(): ENUM_DISCOUNT_TYPE | undefined {
        return this._cartDiscountType as ENUM_DISCOUNT_TYPE;
    }
    set cartDiscountType(val: ENUM_DISCOUNT_TYPE | string | undefined) {
        this._cartDiscountType = ['percentage'].includes(val?.toLowerCase()) ? ENUM_DISCOUNT_TYPE.PERCENTAGE : ['flat'].includes(val?.toLowerCase()) ? ENUM_DISCOUNT_TYPE.FLAT : undefined;;
    }

    get cartDiscountAmount(): number | undefined {
        return this._cartDiscountAmount ? parseFloat(this._cartDiscountAmount) : undefined;
    }
    set cartDiscountAmount(val: number | string | undefined) {
        this._cartDiscountAmount = val?.toString();
    }

    get paymentMethod(): ENUM_PAYMENT_METHOD {
        return this._paymentMethod as ENUM_PAYMENT_METHOD;
    }
    set paymentMethod(val: ENUM_PAYMENT_METHOD | string) {
        this._paymentMethod = val;
    }

    get paymentStatus(): ENUM_PAYMENT_STATUS {
        return this._paymentStatus as ENUM_PAYMENT_STATUS;
    }
    set paymentStatus(val: ENUM_PAYMENT_STATUS | string) {
        this._paymentStatus = val;
    }

    get invoiceItemId(): string {
        return this._invoiceItemId;
    }
    set invoiceItemId(val: string) {
        this._invoiceItemId = val;
    }

    get itemId(): string {
        return this._itemId;
    }
    set itemId(val: string) {
        this._itemId = val;
    }

    get itemType(): ENUM_PRODUCT_ITEM_TYPE {
        return this._itemType.trim() as ENUM_PRODUCT_ITEM_TYPE;
    }
    set itemType(val: ENUM_PRODUCT_ITEM_TYPE | string) {
        this._itemType = val.trim();
    }

    get itemQuantity(): number {
        return parseInt(this._itemQuantity, 10);
    }
    set itemQuantity(val: number | string) {
        this._itemQuantity = typeof val === "string" ? val : val.toString();
    }

    get promotion(): string | undefined {
        return this._promotion;
    }
    set promotion(val: string | undefined) {
        this._promotion = val;
    }

    get itemDiscountType(): ENUM_DISCOUNT_TYPE | undefined {
        return this._itemDiscountType as ENUM_DISCOUNT_TYPE;
    }
    set itemDiscountType(val: ENUM_DISCOUNT_TYPE | string | undefined) {
        this._itemDiscountType = ['percentage'].includes(val?.toLowerCase()) ? ENUM_DISCOUNT_TYPE.PERCENTAGE : ['flat'].includes(val?.toLowerCase()) ? ENUM_DISCOUNT_TYPE.FLAT : undefined;
    }

    get itemDiscountAmount(): number | undefined {
        return this._itemDiscountAmount ? parseFloat(this._itemDiscountAmount) : undefined;
    }
    set itemDiscountAmount(val: number | string | undefined) {
        this._itemDiscountAmount = val?.toString();
    }

    get startDate(): Date | undefined {
        return this._startDate ? new Date(this._startDate) : undefined;
    }
    set startDate(val: string | Date | undefined) {
        if (!val) {
            this._startDate = undefined;
        } else {
            this._startDate = typeof val === "string" ? val : val.toISOString();
        }
    }

    get isBusiness(): boolean | undefined {
        if (this._isBusiness === undefined) return undefined;
        return this._isBusiness?.toLowerCase() === "true" || this._isBusiness?.toLowerCase() === "1";
    }
    set isBusiness(val: boolean | string | undefined) {
        if (val === undefined) {
            this._isBusiness = undefined;
        } else {
            this._isBusiness = val.toString();
        }
    }

    get employeeId(): string | undefined {
        return this._employeeId;
    }
    set employeeId(val: string | undefined) {
        this._employeeId = val;
    }

    get voucherCode(): string | undefined {
        return this._voucherCode;
    }
    set voucherCode(val: string | undefined) {
        this._voucherCode = val;
    }

    get isReturnItem(): boolean | undefined {
        if (this._isReturnItem === undefined) return undefined;
        return [true, "true", "1", "yes", "y"].includes(this._isReturnItem?.toLowerCase());
    }
    set isReturnItem(val: boolean | string | undefined) {
        if (val === undefined) {
            this._isReturnItem = undefined;
        } else {
            this._isReturnItem = val.toString();
        }
    }
}