import { Types } from "mongoose";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { IService, Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { ClassType, SessionType } from "./pricing.model";
import { AppointmentType, IAppointmentType } from "./appointment-type.model";
import LoggerConfig from "../../common/logger/log.module";

const logger = LoggerConfig('service-category.migration');
interface ICsvServiceCategory {
  id: string;
  name: string;
  type: string;
  isActive?: boolean;
  image?: string;
  description?: string;
  shortDescription?: string;
  onlineBookingAllowed?: boolean;
}

const CsvToObjectKeyMapServiceCategory: Record<keyof ICsvServiceCategory, string> = {
  id: "id",
  name: "name",
  type: "type",
  isActive: "is active",
  image: "image",
  description: "description",
  shortDescription: "short description",
  onlineBookingAllowed: "online booking allowed"
}

/**
 * Validate service data from CSV
 * @param csvService Service data from CSV
 * @returns Array of validation error messages
 */
function validateServiceData(csvService: ICsvServiceCategory): string[] {
  const errors: string[] = [];

  if (!csvService.id) {
    errors.push("Service ID is required");
  }

  if (!csvService.name) {
    errors.push("Service name is required");
  }

  if (!csvService.type) {
    errors.push("Service type is required");
  } else if (!Object.values(ClassType).includes(csvService.type as ClassType)) {
    errors.push(`Invalid service type: ${csvService.type}. Valid values are: ${Object.values(ClassType).join(', ')}`);
  }

  return errors;
}

/**
 * Migrate services data from CSV to MongoDB
 */
export async function migrateServicesCategory(_dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting services migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get services data from CSV
      const csvServices = await parseCSV<ICsvServiceCategory>(`service-categories.csv`, CsvToObjectKeyMapServiceCategory);

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvServices.forEach((service, index) => {
        const errors = validateServiceData(service);
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error("Validation errors in services data:", validationErrors);
        throw new Error("Invalid services data in CSV");
      }

      // Get all services from database to create name to ObjectId mapping
      const existingServices = await Service.find({ organizationId: global.config.organizationId }).session(session);
      const existingServiceIds = existingServices.map(s => s.id);
      const existingServiceMap = new Map<string, IService>();
      existingServices.forEach(s => {
        existingServiceMap.set(s._id.toString(), s);
        if (s.id) {
          existingServiceMap.set(s.id.toString(), s);
        }
      });


      // Transform data for MongoDB
      const serviceCategoryBulkOp = csvServices.map((csvService) => {
        let serviceAppointmentTypes = [];
        const selectQuery = { organizationId: global.config.organizationId };
        let service = existingServiceMap.get(csvService.id);
        if (service) {
          serviceAppointmentTypes = service.appointmentType || [];
          selectQuery["_id"] = service._id;
        } else {
          service = new Service({});
          selectQuery["id"] = csvService.id;
        }

        // Prepare the fields to update or insert
        const serviceData = {
          id: csvService.id,
          name: csvService.name,
          description: csvService.description || "",
          shortDescription: csvService.shortDescription || "",
          image: csvService.image || "",
          classType: csvService.type as ClassType,
          appointmentType: serviceAppointmentTypes,
          isFeatured: true,
          isActive: !!csvService.isActive,
          createdBy: global.config.organizationId,
          organizationId: global.config.organizationId,
          onlineBookingAllowed: !!csvService.onlineBookingAllowed,
        };

        // Update or insert
        return {
          updateOne: {
            filter: selectQuery,
            update: { $set: serviceData },
            upsert: true
          }
        };
      });

      // Insert services
      if (serviceCategoryBulkOp.length > 0) {
        await Service.bulkWrite(serviceCategoryBulkOp, { ordered: true, session });
      }
      logger.log(`Successfully migrated ${serviceCategoryBulkOp.length} services to MongoDB`);

      // Create a mapping of CSV ID to MongoDB ObjectId for reference
      const serviceIdMapping: { [csvId: string]: string } = {};
      csvServices.forEach((csvService) => {
        const service = existingServiceMap.get(csvService.id);
        if (service) {
          serviceIdMapping[csvService.id] = service._id.toString();
        }
      });

      logger.log("Service ID mapping (CSV ID -> MongoDB ObjectId):");
      Object.entries(serviceIdMapping).forEach(([csvId, mongoId]) => {
        logger.log(`  ${csvId} -> ${mongoId}`);
      });

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating services:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
