import { Types } from "mongoose";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { IService, Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { ClassType, SessionType } from "./pricing.model";
import { AppointmentType, IAppointmentType } from "./appointment-type.model";
import LoggerConfig from "../../common/logger/log.module";

const logger = LoggerConfig('service-category.migration');
interface ICsvServiceCategory {
  id: string;
  name: string;
  type: string;
  isActive?: boolean;
  image?: string;
  description?: string;
  shortDescription?: string;
}

interface ICsvAppointmentType {
  id: string;
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive?: boolean;
  image?: string;
  serviceCategoryId: string;
  isFeatured?: boolean;
  description?: string;
  shortDescription?: string;
}

const CsvToObjectKeyMapAppointmentType: Record<keyof ICsvAppointmentType, string> = {
  id: "id",
  name: "name",
  durationInMinutes: "duration in minutes",
  onlineBookingAllowed: "online booking allowed",
  isActive: "is active",
  image: "image",
  serviceCategoryId: "service category Id",
  isFeatured: "is featured",
  description: "description",
  shortDescription: "short description"
}

/**
 * Validate service data from CSV
 * @param csvService Service data from CSV
 * @returns Array of validation error messages
 */
function validateServiceData(csvService: ICsvServiceCategory): string[] {
  const errors: string[] = [];

  if (!csvService.id) {
    errors.push("Service ID is required");
  }

  if (!csvService.name) {
    errors.push("Service name is required");
  }

  if (!csvService.type) {
    errors.push("Service type is required");
  } else if (!Object.values(ClassType).includes(csvService.type as ClassType)) {
    errors.push(`Invalid service type: ${csvService.type}. Valid values are: ${Object.values(ClassType).join(', ')}`);
  }

  return errors;
}

/**
 * Migrate services data from CSV to MongoDB
 */
export async function migrateServices(_dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting services migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {

      // Get appointment types data to include during service creation
      const csvAppointmentTypes = await parseCSV<ICsvAppointmentType>("services.csv", CsvToObjectKeyMapAppointmentType);

      // Group appointment types by service ID
      const appointmentTypesByServiceId: { [serviceId: string]: IAppointmentType[] } = {};
      const appointmentTypesBlkOp: {
        updateOne: {
          filter: { _id: string };
          update: { $set: { appointmentType: IAppointmentType[] } }
          upsert: boolean;
        }
      }[] = [];

      const existingAppointmentTypes = await AppointmentType.find({ organizationId: global.config.organizationId }).session(session);
      const existingAppointmentTypeIds = existingAppointmentTypes.map(apt => apt.id);
      const existingAppointmentMap = new Map<string, IAppointmentType>();
      existingAppointmentTypes.forEach(apt => {
        existingAppointmentMap.set(apt._id.toString(), apt);
        if (apt.id) {
          existingAppointmentMap.set(apt.id.toString(), apt);
        }
      });
      csvAppointmentTypes.forEach((appointmentType: ICsvAppointmentType) => {
        if (existingAppointmentTypeIds.includes(appointmentType.id)) {
          logger.log(`Appointment type ${appointmentType.id} already exists. Skipping...`);
          return;
        }
      });

      // Get all services from database to create name to ObjectId mapping
      const serviceCategoryBulkOpMap: { [serviceId: string]: { updateOne: any } } = {};
      const existingServiceCategory = await Service.find({ organizationId: global.config.organizationId }).session(session);
      const existingServiceIds = existingServiceCategory.map(s => s.id);
      const existingServiceCategoryMap = new Map<string, IService>();
      existingServiceCategory.forEach(s => {
        existingServiceCategoryMap.set(s._id.toString(), s);
        if (s.id) {
          existingServiceCategoryMap.set(s.id.toString(), s);
        }
      });

      csvAppointmentTypes.forEach((appointmentType: ICsvAppointmentType) => {

        const apt = new AppointmentType({
          organizationId: global.config.organizationId,
          id: appointmentType.id,
          name: appointmentType.name,
          durationInMinutes: appointmentType.durationInMinutes,
          onlineBookingAllowed: !!appointmentType.onlineBookingAllowed,
          isActive: !!appointmentType.isActive,
          image: appointmentType.image || "",
          isFeatured: !!appointmentType.isFeatured,
          shortDescription: appointmentType.shortDescription,
          description: appointmentType.description || ""
        });


        appointmentType.serviceCategoryId.split(",").forEach((serviceId) => {
          if (!appointmentTypesByServiceId[serviceId]) {
            appointmentTypesByServiceId[serviceId] = [];
          }

          // Add appointment type in service
          const serviceCategory = existingServiceCategoryMap.get(serviceId);
          if (!serviceCategory) {
            logger.log(`Service category ${serviceId} not found for appointment type ${appointmentType.id}. Skipping...`);
            return;
          }
          if (!serviceCategory.appointmentType) {
            serviceCategory.appointmentType = [];
          }

          let existingApts = serviceCategory.appointmentType || [];
          let isAlreadyAdded = (existingApts).find(existingApt => ((apt?.id?.toString() === appointmentType?.id?.toString()) && existingApt.id) || (existingApt._id.toString() === apt?.id?.toString()));
          if (isAlreadyAdded) {
            apt._id = isAlreadyAdded._id;
            apt.id = isAlreadyAdded.id;
            existingApts = existingApts.filter(apt => apt._id.toString() !== isAlreadyAdded._id.toString());
          }

          if (serviceCategoryBulkOpMap[serviceId]) {
            existingApts = serviceCategoryBulkOpMap[serviceId].updateOne.update.$set.appointmentType;
          }

          serviceCategory.appointmentType = [...existingApts, apt];
          serviceCategoryBulkOpMap[serviceId] = {
            updateOne: {
              filter: { _id: serviceCategory._id },
              update: { $set: { appointmentType: serviceCategory.appointmentType } }
            }
          };

          appointmentTypesByServiceId[serviceId].push(apt);
        });

      });

      // Update services with appointment types
      if (Object.values(serviceCategoryBulkOpMap).length > 0) {
        await Service.bulkWrite(Object.values(serviceCategoryBulkOpMap), { ordered: true, session });
      }

      // Log appointment types added to each service
      Object.values(serviceCategoryBulkOpMap).forEach((service, index) => {
        const csvService = csvAppointmentTypes[index];
        logger.log(`Service "${service.updateOne.filter._id}" (CSV ID: ${csvService.id}) created appointment types`);

      });

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating services:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
