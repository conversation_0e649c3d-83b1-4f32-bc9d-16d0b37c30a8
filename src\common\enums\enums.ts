export enum ENUM_GENDER {
    MALE = 'male',
    FEMALE = 'female',
    OTHER = 'other',
    UNKNOWN = '',
}

export enum ENUM_RELATION {
    FATHER = 'father',
    MOTHER = 'mother',
    SISTER = 'sister',
    BROTHER = 'brother',
    AUNTY = 'aunty',
    UNCLE = 'uncle',
    G<PERSON><PERSON>_FATHER = 'grand father',
    GRAND_MOTHER = 'grand mother',
    GUARDIAN = 'guardian',
    HUSBAND = 'husband',
    WIFE = 'wife',
    SON = 'son',
    DAUGHTER = 'daughter',
    SPOUSE = 'spouse',
    PARTNER = 'partner',
    FRIEND = 'friend',
    COUSIN = 'cousin',
    NEPHEW = 'nephew',
    NIECE = 'niece',
    G<PERSON><PERSON>_SON = 'grand son',
    G<PERSON><PERSON>_DAUGHTER = 'grand daughter',
    GRAND_CHILD = 'grand child',
    GRAND_PARENT = 'grand parent',
    PARENT = 'parent',
    CHILD = 'child',
    SIBLING = 'sibling',
    RELATIVE = 'relative',
    LEGAL_GUARDIAN = 'legal guardian',
    OTHER = 'other',
}

export enum ENUM_PRODUCT_ITEM_TYPE {
    PRODUCT = 'product', // Product from inventory
    SERVICE = 'service', // Service from pricing
    CUSTOM_PACKAGE = 'custom_package', // Custom package from custom package
    VOUCHER = 'voucher', // Voucher from voucher
}

export enum ENUM_DISCOUNT_TYPE {
    FLAT = "Flat",
    PERCENTAGE = "Percentage",
}

export enum ENUM_PAYMENT_STATUS {
    PENDING = "pending",
    COMPLETED = "completed",
    FAILED = "failed",
    REFUNDED = 'refund',
    CANCELED = 'cancel'
}

export enum ENUM_PAYMENT_METHOD {
    CREDIT_CARD = 'credit_card',
    DEBIT_CARD = 'debit_card',
    PAYPAL = 'paypal',
    CASH = 'cash',
    BANK_TRANSFER = 'bank_transfer',
    UPI = 'upi',
    OTHER = 'other',
}

export enum ENUM_DURATION_UNIT {
    DAYS = "days",
    MONTHS = "months",
    YEARS = "years",
}

export enum ENUM_SESSION_TYPE {
    SINGLE = "single",
    MULTIPLE = "multiple",
    UNLIMITED = "unlimited",
    DAY_PASS = 'day_pass',
    ONCEADAY = "once_a_day",

}



export enum Attributes {
    Brand = "brand",
    Shade = "shade",
    ProductForm = "productForm",
    packageType = "packageType",
    ProductTags = "productTags",
    SizeInKG = "kg",
    SizeInGM = "gm",
    SizeInL = "litre",
    SizeInML = "ml",
    SizeInUnit = "unit",
    SizeInTablet = "tablet",
    SizeInCapsule = "capsule",
    Ingredients = "ingredients",
    Dosage = "dosage",
    Gender = "gender",
    AgeGroup = "ageGroup",
    HairType = "hairType",
    SkinType = "skinType",
    SkinTone = "skinTone",
    Occasion = "occasion",
    Finish = "finish",
    Concern = "concern",
    SPF = "spf",
    Coverage = "coverage",
    FragranceFamily = "fragranceFamily",
    Uses = "uses",
    OriginCountry = "originCountry",
    Preference = "preference",
    Conscious = "conscious",
    CommonSideEffects = "commonSideEffects",
    AlcoholInteraction = "alcoholInteraction",
    PregnancyInteraction = "pregnancyInteraction",
    LactationInteraction = "lactationInteraction",
    DrivingInteraction = "drivingInteraction",
    KidneyInteraction = "kidneyInteraction",
    LiverInteraction = "liverInteraction",
    MedicineType = "medicineType",
    storage = "storage",
    ProductFormQuantity = "quantity",
    FactBox = "factBox",
    Packaging = "packaging",
    Benefits = "benefits",
    SafetyAdvice = "safetyAdvice",
    ProductHighlights = "productHighlights",
    ProductSpecifications = "productSpecifications",
    ManufacturerAddress = "manufacturerAddress",
    ManufacturedBy = "manufacturedBy",
    ImportedBy = "importedBy",
    AboutBrand = "aboutBrand",
    AllergenInformation = "allergenInformation",
    Features = "features",
    GoodToKnow = "goodToKnow",
    IdealFor = "idealFor",
    QuickTips = "quickTips",
    PrescriptionRequired = "prescriptionRequired",
    IfMiss = "ifMiss",
    Formulation = "formulation",
    AreaOfApplication = "areaOfApplication",
    CompatibleWith = "compatibleWith",
    FragranceNotes = "fragranceNotes",
    PackSize = "packSize",
    Flavour = "flavour",
}


export enum InputFieldType {
    Dropdown = "dropdown",
    TextBox = "textBox",
}


export enum AttributeType {
    Mandatory = "mandatory",
    Optional = "optional",
    Variant = "variant",
}