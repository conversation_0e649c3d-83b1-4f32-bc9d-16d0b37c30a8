import path from "path";
import log4js from "log4js";
import fs from "fs";

// Ensure logs folder exists
const logDir = path.resolve("logs");
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// Reuse log4js configuration
const configureLogger = (moduleName = "default") => {
  const date = new Date();
  const dateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;

  const filename = path.join(logDir, `${dateStr}.${moduleName ? moduleName + "." : ""}application.log`);

  log4js.configure({
    appenders: {
      console: { type: "console" },
      file: { type: "file", filename },
    },
    categories: {
      default: { appenders: ["console", "file"], level: "info" },
    },
  });

  return log4js.getLogger(moduleName);
};

// const logger = configureLogger();
export default configureLogger;
// export default logger;
