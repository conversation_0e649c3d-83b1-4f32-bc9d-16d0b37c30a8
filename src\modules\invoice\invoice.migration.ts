import { Types } from 'mongoose';
import LoggerConfig from '../../common/logger/log.module';
import { connectToMongo, closeConnection, getCollection } from '../../common/database/db.module';
import { parseCSV } from '../../common/utils/csv-parser';
import { CsvWriter } from '../../common/utils/csv-writer';
import { MigrationTrackerService } from '../migration/migration-tracker.service';
import { MigrationStatus } from '../migration/migration-tracker.model';
import * as crypto from 'crypto';
import * as path from 'path';
import { Facility, IFacility } from '../facility/facility.model';
import { IPricing, Pricing } from '../pricing/pricing.model';
import { ENUM_PRODUCT_ITEM_TYPE, ENUM_PAYMENT_METHOD, ENUM_PAYMENT_STATUS, ENUM_DISCOUNT_TYPE } from '../../common/enums/enums';
import { CsvToObjectKeyMapUser, ICsvInvoice, IInvoiceItem, IInvoicePurchase, IErrorCsvInvoice } from './invoice.interface';
import { getAllClients, getAllCustomPackage, getAllInventory, getAllPaymentMethod, getAllPricing, getAllProcessedInvoice, getAllProduct, getAllUsers, getVoucherPurchasedAll } from './invoice.migration.service';
import { IClient } from '../user/client.model';
import { IUser } from '../user/user.model';
import { IPurchase, Purchase } from './purchase.model';
import { IPaymentMethod } from './payment-method.model';
import { InvoiceGenerator, InvoiceGeneratorOptions } from './invoice-generator';
import { IBillingDetails, IClientBillingDetails, IClientDetails, IInvoicePurchaseItem, IPaymentDetail } from './invoice.model';
import { ICustomPackageDocument } from '../pricing/custom-package.model';
import { IProduct } from '../products/products.model';
import { IInventory } from '../products/inventory.model';

/**
 * Helper function to check if an error is a transient transaction error
 */
function isTransientTransactionError(error: any): boolean {
  return error?.errorLabelSet?.has('TransientTransactionError') ||
    error?.code === 251 || // NoSuchTransaction
    error?.codeName === 'NoSuchTransaction';
}

/**
 * Helper function to retry operations with transient transaction errors
 */
async function retryTransientErrors<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (isTransientTransactionError(error) && attempt < maxRetries) {
        logger.warn(`Transient transaction error on attempt ${attempt}, retrying in ${delayMs}ms...`, error.message);
        await new Promise(resolve => setTimeout(resolve, delayMs));
        delayMs *= 2; // Exponential backoff
        continue;
      }

      throw error;
    }
  }

  throw lastError;
}
import { IVoucherRedemption } from './voucherRedemption.model';
import { VoucherRedemption } from './voucherRedemption.model';
import { IMembership, Membership } from '../organization/membership.model';
import { CsvInvoice } from './invoice-csv.class';

/**
 * Interface for tracking failed invoice migrations
 */
interface IFailedInvoiceMigration {
  invoiceId: string;
  rowNumbers: number[];
  errorMessage: string;
  errorType: 'VALIDATION_ERROR' | 'PROCESSING_ERROR' | 'DATABASE_ERROR' | 'UNKNOWN_ERROR';
  timestamp: Date;
  originalData?: any;
}

/**
 * Interface for migration statistics
 */
interface IMigrationStats {
  totalInvoices: number;
  successfulInvoices: number;
  failedInvoices: number;
  skippedInvoices: number;
  totalItems: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  mode: string;
}



const alphanumericCharacters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

const logger = LoggerConfig('invoice.migration');

/**
 * Categorize error type based on error message and properties
 */
function categorizeError(error: any): 'VALIDATION_ERROR' | 'PROCESSING_ERROR' | 'DATABASE_ERROR' | 'UNKNOWN_ERROR' {
  if (!error) return 'UNKNOWN_ERROR';

  const errorMessage = error.message?.toLowerCase() || '';

  if (errorMessage.includes('not found') ||
    errorMessage.includes('invalid') ||
    errorMessage.includes('validation') ||
    errorMessage.includes('required')) {
    return 'VALIDATION_ERROR';
  }

  if (errorMessage.includes('transaction') ||
    errorMessage.includes('database') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('timeout') ||
    error.code) {
    return 'DATABASE_ERROR';
  }

  if (errorMessage.includes('processing') ||
    errorMessage.includes('calculation') ||
    errorMessage.includes('generation')) {
    return 'PROCESSING_ERROR';
  }

  return 'UNKNOWN_ERROR';
}

/**
 * Generate migration results including error CSV and summary
 */
async function generateMigrationResults(
  failedInvoices: IFailedInvoiceMigration[],
  migrationStats: IMigrationStats,
  originalCsvData: ICsvInvoice[]
): Promise<void> {
  try {
    logger.info('Generating migration result files...');

    // Create processed directory if it doesn't exist
    const processedDir = path.join(process.env.DATA_FOLDER || 'data', 'migration-results', 'invoice-migration-results');

    // Create timestamped filenames
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const errorFile = path.join(processedDir, `failed-invoices-${timestamp}.csv`);
    const summaryFile = path.join(processedDir, `migration-summary-${timestamp}.csv`);

    // Generate error CSV if there are failed invoices
    if (failedInvoices.length > 0) {
      const errorRecords: IErrorCsvInvoice[] = [];

      for (const failedInvoice of failedInvoices) {
        // Find all CSV rows for this failed invoice
        const invoiceRows = originalCsvData.filter(row => row.invoiceId === failedInvoice.invoiceId);

        for (const row of invoiceRows) {
          errorRecords.push({
            ...row,
            errorMessage: failedInvoice.errorMessage,
            errorType: failedInvoice.errorType,
            timestamp: failedInvoice.timestamp.toISOString()
          });
        }
      }

      await CsvWriter.writeToFile(errorRecords, errorFile);
      logger.info(`Generated error CSV: ${errorFile} (${errorRecords.length} records)`);
    } else {
      logger.info('No failed invoices to write to error CSV');
    }

    // Generate summary CSV
    const summaryRecords = [{
      mode: migrationStats.mode,
      totalInvoices: migrationStats.totalInvoices,
      successfulInvoices: migrationStats.successfulInvoices,
      failedInvoices: migrationStats.failedInvoices,
      skippedInvoices: migrationStats.skippedInvoices,
      totalItems: migrationStats.totalItems,
      successRate: ((migrationStats.successfulInvoices / migrationStats.totalInvoices) * 100).toFixed(2) + '%',
      startTime: migrationStats.startTime.toISOString(),
      endTime: migrationStats.endTime?.toISOString() || 'N/A',
      durationMs: migrationStats.duration || 0,
      durationMinutes: migrationStats.duration ? (migrationStats.duration / 60000).toFixed(2) : '0'
    }];

    await CsvWriter.writeToFile(summaryRecords, summaryFile);
    logger.info(`Generated migration summary CSV: ${summaryFile}`);

    // Log final statistics
    logger.info('=== INVOICE MIGRATION SUMMARY ===');
    logger.info(`Mode: ${migrationStats.mode}`);
    logger.info(`Total Invoices: ${migrationStats.totalInvoices}`);
    logger.info(`Successful: ${migrationStats.successfulInvoices}`);
    logger.info(`Failed: ${migrationStats.failedInvoices}`);
    logger.info(`Skipped: ${migrationStats.skippedInvoices}`);
    logger.info(`Success Rate: ${((migrationStats.successfulInvoices / migrationStats.totalInvoices) * 100).toFixed(2)}%`);
    logger.info(`Total Items Processed: ${migrationStats.totalItems}`);
    logger.info(`Duration: ${migrationStats.duration ? (migrationStats.duration / 60000).toFixed(2) : '0'} minutes`);
    logger.info('================================');

  } catch (error) {
    logger.error('Error generating migration results:', error);
    throw error;
  }
}

/**
 * Migrate invoices data from CSV to MongoDB
 * @param _dbName Database name
 * @param mode Migration mode: 'create', 'update', or 'upsert'
 */
export async function migrateInvoices(_dbName: string = 'hop-migration', mode: 'create' | 'update' | 'upsert' = 'update'): Promise<void> {
  // Migration tracking variables
  const failedInvoices: IFailedInvoiceMigration[] = [];
  const migrationStats: IMigrationStats = {
    totalInvoices: 0,
    successfulInvoices: 0,
    failedInvoices: 0,
    skippedInvoices: 0,
    totalItems: 0,
    startTime: new Date(),
    mode: mode
  };

  logger.log(`Starting invoices migration in ${mode} mode...`);

  // Connect to database
  await connectToMongo();

  // Get mongoose instance for session management
  const mongoose = require('mongoose');

  const userIdMap: Map<string, IUser> = new Map();
  const clientIdMap: Map<string, IClient> = new Map();
  const pricingMap: Map<string, IPricing> = new Map();
  const customPackageMap: Map<string, ICustomPackageDocument> = new Map();
  const productMap: Map<string, IProduct> = new Map();
  const inventoryMap: Map<string, IInventory> = new Map();
  const paymentMethodMap: Map<string, IPaymentMethod> = new Map();

  const purchaseItemsMap: Map<string, IPurchase[]> = new Map();
  const voucherPurchasedMap: Map<string, IPurchase> = new Map();

  const membershipIdMap: Map<string, IMembership> = new Map();

  const voucherAppliedIdSet: Set<string> = new Set();

  const allUserIds = new Set();
  const allPricingIds = new Set();
  const allCustomPackageIds = new Set();
  const allProductIds = new Set();
  const getAllPaymentMethodIds = new Set();

  const processedInvoiceIds = new Set<string>();

  try {
    // Get invoices data from CSV
    // const csvInvoicesRow = await parseCSV<ICsvInvoice>('invoices-test.csv', CsvToObjectKeyMapUser);
    const csvInvoicesRow = await parseCSV<ICsvInvoice>('invoices-for 03-12-25-updated.csv', CsvToObjectKeyMapUser);
    const csvInvoices = csvInvoicesRow.map(row => {
      const invoice = new CsvInvoice(row)
      invoice.isReturnItem = row.amountPaid < 0 || row.itemQuantity < 0;
      return invoice;
    });
    const itemsGroupedByInvoiceId: Record<string, ICsvInvoice[]> = csvInvoices.reduce((acc, invoice, index) => {
      if (!acc[invoice.invoiceId]) {
        acc[invoice.invoiceId] = [];
      }
      invoice.index = index;
      acc[invoice.invoiceId].push(invoice);
      return acc;
    }, {});


    try {
      // Process all invoices in a single transaction
      const invoiceIds = Object.keys(itemsGroupedByInvoiceId);
      logger.log(`Processing ${invoiceIds.length} invoices in a single transaction`);

      const allInvoices = invoiceIds.map(invoiceId => itemsGroupedByInvoiceId[invoiceId]);

      const rawUserIds = new Set();
      const rawPricingIds = new Set();
      const rawCustomPackageIds = new Set();
      const rawProductIds = new Set();
      const rawVoucherCodes = new Set<string>();
      const rawMembershipIdsSet = new Set();

      // prepare data
      for (const invoice of allInvoices) {
        invoice.forEach(csvItem => {
          if ((csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE || csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER)) {
            allPricingIds.add(csvItem.itemId);
            rawPricingIds.add(csvItem.itemId);
          } else if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE) {
            rawCustomPackageIds.add(csvItem.itemId);
            allCustomPackageIds.add(csvItem.itemId);
          } else if (csvItem.itemType == ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
            allProductIds.add(csvItem.itemId);
            rawProductIds.add(csvItem.itemId);
          } else {
            logger.error(`Row ${csvItem.index + 1}: Invalid item type ${csvItem.itemType} for invoice ${invoice[0].invoiceId}`);
            throw new Error(` ${csvItem.index + 1}: Invalid item type ${csvItem.itemType} for invoice ${invoice[0].invoiceId}`);
          }
          if (!getAllPaymentMethodIds.has(csvItem.paymentMethod)) {
            getAllPaymentMethodIds.add(csvItem.paymentMethod);
          }
          if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) {
            rawVoucherCodes.add(csvItem.voucherCode);
          }
        });
        rawUserIds.add(invoice[0].userId);
        allUserIds.add(invoice[0].userId);
        if (invoice[0].billingClientId) {
          rawUserIds.add(invoice[0].billingClientId);
          allUserIds.add(invoice[0].billingClientId);
        }
        if (invoice[0].employeeId) {
          rawUserIds.add(invoice[0].employeeId);
          allUserIds.add(invoice[0].employeeId);
        }
      }

      const [users, pricing, customPackage, products, inventory, paymentMethods, voucherPurchased, processedInvoice]: [IUser[], IPricing[], ICustomPackageDocument[], IProduct[], IInventory[], IPaymentMethod[], IPurchase[], any[]] = await Promise.all([
        getAllUsers([...rawUserIds]),
        getAllPricing([...rawPricingIds]),
        getAllCustomPackage([...rawCustomPackageIds]),
        getAllProduct([...rawProductIds]),
        getAllInventory([...rawProductIds]),
        getAllPaymentMethod([...getAllPaymentMethodIds]),
        getVoucherPurchasedAll([...rawVoucherCodes]),
        getAllProcessedInvoice(global.config.organizationId),
      ]);

      users.forEach(u => {
        userIdMap.set(u._id.toString(), u);
        userIdMap.set(u.id.toString(), u);
        userIdMap.set(u.email.toString(), u);
        userIdMap.set(u.mobile.toString(), u);
      });

      pricing.forEach(p => {
        pricingMap.set(p._id.toString(), p);
        if (p.id) {
          pricingMap.set(p.id.toString(), p);
        }
        if (p.membershipId) {
          rawMembershipIdsSet.add(p.membershipId.toString());
        }
      });

      customPackage.forEach(p => {
        customPackageMap.set(p._id.toString(), p);
        if (p.id) {
          customPackageMap.set(p.id.toString(), p);
        }
      });

      products.forEach(p => {
        productMap.set(p._id.toString(), p);
        if (p.migrationId) {
          productMap.set(p.migrationId.toString(), p);
        }
      });

      inventory.forEach(p => {
        inventoryMap.set(p._id.toString(), p);
        inventoryMap.set(p.productId.toString(), p);
        // if (p.id) {
        //   inventoryMap.set(p.id.toString(), p);
        // }
      });

      paymentMethods.forEach(p => {
        paymentMethodMap.set(p.shortId, p);
      });

      const clients = await getAllClients(users.map(u => u._id));
      clients.forEach(c => {
        clientIdMap.set(c.userId.toString(), c);
      });

      voucherPurchased.forEach(v => {
        voucherPurchasedMap.set(v.voucherCode, v);
      });

      processedInvoice.forEach(i => {
        voucherAppliedIdSet.add(`${global.config.organizationId.toString()}-${i.orderId}`);
      });

      const memberships = await Membership.find({
        organizationId: global.config.organizationId,
        _id: { $in: [...rawMembershipIdsSet] }
      }).exec();
      memberships.forEach(m => {
        membershipIdMap.set(m._id.toString(), m);
        if (m.id) {
          membershipIdMap.set(m.id.toString(), m);
        }
      });

      // >> Process to one invoice data
      let invoiceCount = 0;
      migrationStats.totalInvoices = allInvoices.length;
      migrationStats.totalItems = csvInvoices.length;

      for (const invoiceRawData of allInvoices) {
        const currentInvoiceId = invoiceRawData[0].invoiceId;
        const rowNumbers = invoiceRawData.map(item => item.index + 2); // +2 because CSV is 1-indexed and has header

        try {
          const invoiceExists = voucherAppliedIdSet.has(`${global.config.organizationId.toString()}-${currentInvoiceId}`);

          // Handle different modes
          if (mode === 'create') {
            if (invoiceExists) {
              logger.log(`Invoice ${currentInvoiceId} already exists, skipping in create mode`);
              migrationStats.skippedInvoices++;
              continue;
            }
          } else if (mode === 'update') {
            if (!invoiceExists) {
              logger.log(`Invoice ${currentInvoiceId} not found, skipping in update mode`);
              migrationStats.skippedInvoices++;
              continue;
            }
          }
          // For upsert mode, we process all invoices regardless of existence

          // Create a single session for this invoice
          const session = await retryTransientErrors(async () => {
            const newSession = await mongoose.startSession();
            newSession.startTransaction();
            return newSession;
          });
          try {
            const facility: IFacility = global.facilityMap.get(invoiceRawData[0].facilityId) ?? global.facilityMap.values().next()?.value;
            if (!facility) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: Facility with id ${invoiceRawData[0].facilityId} not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: Facility with id ${invoiceRawData[0].facilityId} not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            const user = userIdMap.get(invoiceRawData[0].userId);
            if (!user) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: User with id ${invoiceRawData[0].userId} not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: User with id ${invoiceRawData[0].userId} not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            const paymentMethod = paymentMethodMap.get(invoiceRawData[0].paymentMethod);
            if (!paymentMethod) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            const amount = Number(invoiceRawData[0].amountPaid);
            // if (amount <= 0 || isNaN(amount)) {
            //   logger.error(`Row ${invoiceRawData[0].index + 1}: Invalid amount for invoice ${amount}`);
            //   throw new Error(`Row ${invoiceRawData[0].index + 1}: Invalid amount for invoice ${amount}`);
            // }

            let paymentDetails: IPaymentDetail[] = [];
            if (invoiceRawData[0].paymentStatus === ENUM_PAYMENT_STATUS.COMPLETED) {
              const paymentMethod = paymentMethodMap.get(invoiceRawData[0].paymentMethod);
              if (!paymentMethod) {
                logger.error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
                throw new Error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
              }

              paymentDetails = [{
                paymentMethod: paymentMethod.shortId,
                paymentMethodId: paymentMethod._id as Types.ObjectId,
                amount: amount,
                paymentDate: new Date(invoiceRawData[0].invoiceDate || new Date()),
                paymentStatus: invoiceRawData[0].paymentStatus,
                paymentGateway: paymentMethod.shortId,
              }];
            }

            const clientDetails: IClientDetails = {
              customerId: user._id as Types.ObjectId,
              name: user.name,
              email: user.email,
              phone: user.mobile,
            }

            const isBusiness = invoiceRawData[0].isBusiness;
            const billingUserId = invoiceRawData[0].billingClientId || user._id.toString();
            const billingUser = userIdMap.get(billingUserId);
            const billingClient = clientIdMap.get(billingUser?._id?.toString());
            const city = global.cityMap.get(billingClient?.address?.city?.toString());
            const state = global.stateMap.get(billingClient?.address?.state?.toString());
            const billingAddress = isBusiness ? billingClient?.businessAddress : billingClient?.address;
            // if (!city) {
            //   logger.error(`Row ${invoiceRawData[0].index + 1}: City not found for invoice ${invoiceRawData[0].invoiceId}`);
            //   throw new Error(`Row ${invoiceRawData[0].index + 1}: City not found for invoice ${invoiceRawData[0].invoiceId}`);
            // }
            if (!state) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            if (!billingUser?.email && !billingUser?.mobile) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            // if (!billingAddress) {
            //   logger.error(`Row ${invoiceRawData[0].index + 1}: Billing address not found for invoice ${invoiceRawData[0].invoiceId}`);
            //   throw new Error(`Row ${invoiceRawData[0].index + 1}: Billing address not found for invoice ${invoiceRawData[0].invoiceId}`);
            // }
            const clientBillingDetails: IClientBillingDetails = {
              customerId: billingUser._id as Types.ObjectId,
              name: billingUser.name,
              addressLine1: billingAddress.addressLine1 || "Address Line 1",
              addressLine2: billingAddress.addressLine2,
              postalCode: billingAddress.postalCode || 123456,
              stateId: billingAddress.state as Types.ObjectId,
              cityId: city?._id as Types.ObjectId,
              cityName: city?.name,
              stateName: state.name,
              email: billingUser.email,
              phone: billingUser.mobile,
              gstNumber: billingAddress.gstNumber,
              utCode: state.gstCode,
            }

            const facilityCity = global.cityMap.get(facility?.address?.city?.toString());
            const facilityState = global.stateMap.get(facility?.address?.state?.toString());
            const employeeId = userIdMap.get(invoiceRawData[0].employeeId)?._id as Types.ObjectId ?? global.config.organizationId;

            const facilityBillingDetails: IBillingDetails = {
              facilityName: facility.facilityName,
              billingName: facility.billingDetails?.billingName ?? "Facility Name",
              gstNumber: facility.billingDetails?.gstNumber ?? "",
              email: facility.email || "<EMAIL>",
              phone: facility.mobile || "1234567890",
              addressLine1: facility?.billingDetails?.addressLine1 || "Address Line 1",
              addressLine2: facility?.billingDetails?.addressLine2 || "",
              postalCode: facility?.billingDetails?.postalCode || 123456,
              cityId: facilityCity?._id as Types.ObjectId,
              cityName: facilityCity?.name,
              stateId: facilityState._id as Types.ObjectId,
              stateName: facilityState.name,
              utCode: facilityState.gstCode,
            }

            // if (!facilityCity) {
            //   logger.error(`Row ${invoiceRawData[0].index + 1}: City not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
            //   throw new Error(`Row ${invoiceRawData[0].index + 1}: City not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
            // }
            if (!facilityState) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: State not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: State not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
            }
            if (!facilityBillingDetails.utCode) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: UT code not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: UT code not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            if (!facilityBillingDetails.stateId) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
            }
            if (!facilityBillingDetails.email && !facilityBillingDetails.phone) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
            }

            const invoiceOptions: InvoiceGeneratorOptions = {
              userId: billingUser._id.toString(),
              organizationId: global.config.organizationId.toString(),
              facilityId: facility._id.toString(),
              createdBy: employeeId.toString(),
              paymentBy: employeeId.toString(),
              invoiceNumber: Number(invoiceRawData[0].invoiceId),
              orderId: Number(invoiceRawData[0].invoiceId),
              platform: 'migration',
              date: invoiceRawData[0].invoiceDate,
              isInclusiveofGst: !!global.organizationSetting?.isInclusiveofGst,
              billingAddressId: billingUser._id.toString(),
              clientDetails,
              clientBillingDetails,
              billingDetails: facilityBillingDetails,
              paymentDetails: paymentDetails,
              isForBusiness: isBusiness,
              cartDiscount: Number(invoiceRawData[0].cartDiscountAmount) || 0,
              cartDiscountType: invoiceRawData[0].cartDiscountType,
            }

            const invoice = new InvoiceGenerator(invoiceOptions);
            const purchaseInvoiceItemsQuantityMap: Map<string, IInvoicePurchaseItem> = new Map<string, IInvoicePurchaseItem>();
            invoiceRawData.forEach(csvItem => {
              const isReturnItem = csvItem.amountPaid < 0 || csvItem.itemQuantity < 0;
              csvItem.amountPaid = Math.abs(csvItem.amountPaid);
              csvItem.itemQuantity = Math.abs(csvItem.itemQuantity);
              if (!isReturnItem && (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE || csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER)) {
                const item = pricingMap.get(csvItem.itemId);
                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Service or voucher Item with id ${csvItem.itemId} not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Service or voucher Item with id ${csvItem.itemId} not found for invoice ${csvItem.invoiceId}`);
                }
                // item.price = csvItem.amountPaid || (item.price * csvItem.itemQuantity);

                if (purchaseInvoiceItemsQuantityMap.has(item.id.toString())) {
                  csvItem.amountPaid += csvItem.amountPaid;
                  csvItem.itemQuantity += csvItem.itemQuantity;
                  invoice.purchaseItems = invoice.purchaseItems.filter(p => p.packageId.toString() !== item._id.toString());
                  invoice.invoice.purchaseItems = invoice.invoice.purchaseItems.filter(p => p.packageId.toString() !== item._id.toString());
                }

                item.price = csvItem.price; // || item.price;
                item.voucherCode = csvItem.voucherCode;
                item.invoiceItemId = csvItem.invoiceItemId;
                const { purchaseItems, invoicePurchaseItem } = invoice.addServiceOrVoucherItem(item,
                  Number(csvItem.itemQuantity), {
                  index: csvItem.index,
                  startDate: csvItem.startDate ?? new Date(),
                  discountType: csvItem.itemDiscountType,
                  discountValue: csvItem.itemDiscountAmount,
                  promotionLabel: csvItem.promotion,
                  promotionLabelKey: csvItem.promotion,
                  amountPaid: csvItem.amountPaid,
                });

                purchaseItemsMap.set(csvItem.invoiceItemId.toString(), purchaseItems);
                purchaseInvoiceItemsQuantityMap.set(item.id.toString(), invoicePurchaseItem);

                if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) {
                  if (purchaseItems.some(item => !item.voucherCode)) {
                    logger.error(`Row ${csvItem.index + 1}: Voucher code ${csvItem.voucherCode} not found for invoice ${csvItem.invoiceId}`);
                    throw new Error(`Row ${csvItem.index + 1}: Voucher code ${csvItem.voucherCode} not found for invoice ${csvItem.invoiceId}`);
                  }
                  purchaseItems.forEach(item => {
                    voucherPurchasedMap.set(item.voucherCode, item);
                  });
                }

                if (item.membershipId) {
                  const membership = membershipIdMap.get(item.membershipId.toString());
                  if (!membership?.lastcounter || membership?.lastcounter == 0) {
                    invoice.memberUserBlkOperation.push({
                      updateOne: {
                        filter: { _id: invoice.invoice.userId },
                        update: { 'profileDetails.membershipId': `${membership.prefix}-${membership.counter}`.toString() },
                      }
                    });
                    invoice.memberClientBlkOperation.push({
                      updateOne: {
                        filter: { userId: invoice.invoice.userId },
                        update: { membershipId: `${membership.prefix}-${membership.counter}`.toString() },
                      }
                    });
                    invoice.memberMembershipBlkOperation.push({
                      updateOne: {
                        filter: { _id: membership._id },
                        update: { lastcounter: membership.counter + 1 },
                      }
                    },);
                    membership.lastcounter = membership.counter + 1;
                    membershipIdMap.set(membership._id.toString(), membership);
                  } else {
                    invoice.memberUserBlkOperation.push({
                      updateOne: {
                        filter: { _id: invoice.invoice.userId },
                        update: { 'profileDetails.membershipId': `${membership.prefix}-${membership.lastcounter}`.toString() },
                      }
                    });
                    invoice.memberClientBlkOperation.push({
                      updateOne: {
                        filter: { userId: invoice.invoice.userId },
                        update: { membershipId: `${membership.prefix}-${membership.lastcounter}`.toString() },
                      }
                    });
                    invoice.memberUserBlkOperation.push({
                      updateOne: {
                        filter: { _id: membership._id },
                        update: { lastcounter: membership.lastcounter + 1 },
                      }
                    });
                    membership.lastcounter = membership.lastcounter + 1;
                    membershipIdMap.set(membership._id.toString(), membership);
                  }
                }

              } else if (!isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE) {
                const item = customPackageMap.get(csvItem.itemId);
                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Custom package Item not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Custom package Item not found for invoice ${csvItem.invoiceId}`);
                }
                item.unitPrice = csvItem.price;
                item.quantity = csvItem.itemQuantity;
                item.name = csvItem.packageName ?? item.name;
                invoice.addCustomPackageItem(item, {
                  index: csvItem.index,
                  discountType: csvItem.itemDiscountType,
                  discountValue: csvItem.itemDiscountAmount,
                  promotionLabel: csvItem.promotion,
                  promotionLabelKey: csvItem.promotion,
                  amountPaid: csvItem.amountPaid,
                });
              } else if (!isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
                const item = productMap.get(csvItem.itemId);
                // const inventory = inventoryMap.get(csvItem.itemId);
                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Product Item not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Product Item not found for invoice ${csvItem.invoiceId}`);
                }
                // if (!inventory) {
                //   logger.error(`Row ${csvItem.index + 1}: Product not found for invoice ${csvItem.invoiceId}`);
                //   throw new Error(`Row ${csvItem.index + 1}: Product not found for invoice ${csvItem.invoiceId}`);
                // }
                const inventory: any = {
                  salePrice: csvItem.price,
                  // variantId: csvItem.variantId,
                };

                invoice.addProductItem(item, {
                  salePrice: inventory.salePrice,
                }, csvItem.itemQuantity, undefined, {
                  index: csvItem.index,
                  discountType: csvItem.itemDiscountType,
                  discountValue: csvItem.itemDiscountAmount,
                  promotionLabel: csvItem.promotion,
                  promotionLabelKey: csvItem.promotion,
                  amountPaid: csvItem.amountPaid,
                });
              }
              // Return service
              // Return custom package
              else if (isReturnItem && csvItem.itemType as any === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) {
                const item = voucherPurchasedMap.get(csvItem.voucherCode);
                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Return Voucher Item not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Return Voucher Item not found for invoice ${csvItem.invoiceId}`);
                }
                const voucher = invoice.applyVoucherDiscount(item, csvItem.amountPaid);
                // purchaseItemsMap.set(csvItem.invoiceItemId.toString(), [voucher]);
                voucherPurchasedMap.set(csvItem.voucherCode, voucher);
              }
              else if (isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE) {
                const item = pricingMap.get(csvItem.itemId);
                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Return Service Item not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Return Service Item not found for invoice ${csvItem.invoiceId}`);
                }
                invoice.returnServiceItem(item,
                  csvItem.amountPaid, Number(csvItem.itemQuantity),
                  {
                    index: csvItem.index,
                    startDate: csvItem.startDate ?? new Date(),
                    // discountType: csvItem.itemDiscountType,
                    // discountValue: csvItem.itemDiscountAmount,
                    promotionLabel: csvItem.promotion,
                    promotionLabelKey: csvItem.promotion,
                  });
              }
              else if (isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE) {
                const item = customPackageMap.get(csvItem.itemId);
                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Return Custom Package Item not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Return Custom Package Item not found for invoice ${csvItem.invoiceId}`);
                }
                invoice.returnCustomPackageItem(item, csvItem.amountPaid, {
                  index: csvItem.index,
                  // discountType: csvItem.itemDiscountType,
                  // discountValue: csvItem.itemDiscountAmount,
                  promotionLabel: csvItem.promotion,
                  promotionLabelKey: csvItem.promotion,
                });
              }
              // Return product
              else if (isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
                const item = productMap.get(csvItem.itemId);

                if (!item) {
                  logger.error(`Row ${csvItem.index + 1}: Return Product Item not found for invoice ${csvItem.invoiceId}`);
                  throw new Error(`Row ${csvItem.index + 1}: Return Product Item not found for invoice ${csvItem.invoiceId}`);
                }

                const inventory = {
                  salePrice: csvItem.amountPaid,
                }

                invoice.returnProductItem(item, inventory,
                  csvItem.amountPaid, csvItem.itemQuantity, undefined, {
                  index: csvItem.index,
                  // discountType: csvItem.itemDiscountType,
                  // discountValue: csvItem.itemDiscountAmount,
                  promotionLabel: csvItem.promotion,
                  promotionLabelKey: csvItem.promotion,
                });
              }

            });

            // << end of one invoice process
            // invoice.execute();

            // Save invoice with retry logic for transient transaction errors
            await retryTransientErrors(async () => {
              if (mode === 'create') {
                await invoice.save({ session });
              } else if (mode === 'update') {
                await invoice.update({ session });
              } else if (mode === 'upsert') {
                await invoice.upsert({ session });
              } else {
                logger.warn(`Not a valid mode: ${mode}. Using default mode: update`);
              }
            });
            processedInvoiceIds.add(`${global.config.organizationId.toString()}-${invoice.invoice.orderId}`);
            await session.commitTransaction();
            invoiceCount++;
            migrationStats.successfulInvoices++;
            logger.info(`Successfully processed invoice ${currentInvoiceId} (${invoiceCount}/${allInvoices.length})`);
          }
          catch (error) {
            logger.error(`Error processing invoice ${currentInvoiceId}:`, error);
            await session.abortTransaction();

            // Record the failed invoice with error details
            const errorType = categorizeError(error);
            failedInvoices.push({
              invoiceId: currentInvoiceId,
              rowNumbers: rowNumbers,
              errorMessage: error.message || 'Unknown error occurred',
              errorType: errorType,
              timestamp: new Date(),
              originalData: invoiceRawData
            });

            migrationStats.failedInvoices++;
            logger.warn(`Failed to process invoice ${currentInvoiceId}, continuing with next invoice...`);
          }
          finally {
            logger.log('Row Processed: ', invoiceCount + migrationStats.failedInvoices);
            await session.endSession();
          }
        } catch (outerError) {
          // Handle errors that occur before session creation
          logger.error(`Error setting up processing for invoice ${currentInvoiceId}:`, outerError);

          const errorType = categorizeError(outerError);
          failedInvoices.push({
            invoiceId: currentInvoiceId,
            rowNumbers: rowNumbers,
            errorMessage: outerError.message || 'Error during invoice setup',
            errorType: errorType,
            timestamp: new Date(),
            originalData: invoiceRawData
          });

          migrationStats.failedInvoices++;
          logger.warn(`Failed to set up processing for invoice ${currentInvoiceId}, continuing with next invoice...`);
        }
      }

      // Set end time and calculate duration
      migrationStats.endTime = new Date();
      migrationStats.duration = migrationStats.endTime.getTime() - migrationStats.startTime.getTime();

      logger.log('All invoices processed successfully');

      // Generate error CSV and summary
      await generateMigrationResults(failedInvoices, migrationStats, csvInvoices);

    } catch (error) {
      // Handle overall migration errors
      logger.error('Error in invoice migration:', error);
      migrationStats.endTime = new Date();
      migrationStats.duration = migrationStats.endTime.getTime() - migrationStats.startTime.getTime();

      // Still try to generate results even if migration failed
      try {
        await generateMigrationResults(failedInvoices, migrationStats, csvInvoices);
      } catch (resultError) {
        logger.error('Error generating migration results:', resultError);
      }

      throw error;
    }
  } catch (error) {
    logger.error('Error migrating invoices:', error);
    throw error;
  } finally {
    // End the session
    // if (session) {
    //   try {
    //     await session.endSession();
    //   } catch (endError) {
    //     logger.error('Error ending session:', endError);
    //   }
    // }
    // Close the connection
    await closeConnection();
  }
}



function shuffleString(str: string): string {
  let arr = str.split('');
  for (let i = arr.length - 1; i > 0; i--) {
    let j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.join('');
}

/**
 * Generates a secure code for a voucher.
 * @returns A secure code.
 */
export async function generateSecureCode(attempts: number = 0): Promise<string> {
  const length = 12; // Fixed length of 12 characters
  let result = '';
  const bytes = crypto.randomBytes(length);

  for (let i = 0; i < length; i++) {
    const index = bytes[i] % shuffleString(alphanumericCharacters).length;
    result += alphanumericCharacters[index];
  }

  // Add hyphens every 4 characters for readability.
  let formattedResult = result.replace(/(.{4})/g, '$1-').slice(0, -1);
  const count = await Purchase.countDocuments({ voucherCode: formattedResult }).lean();
  if (count > 0) {
    if (attempts >= 10) {
      throw new Error('Failed to generate unique voucher code after 10 attempts.');
    }
    return generateSecureCode(attempts + 1);
  }
  return formattedResult;
}
