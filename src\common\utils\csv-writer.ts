import * as fs from 'fs';
import * as path from 'path';
import LoggerConfig from '../logger/log.module';

const logger = LoggerConfig('csv-writer');

/**
 * Interface for CSV writer options
 */
export interface ICsvWriterOptions {
  headers?: string[];
  delimiter?: string;
  includeHeaders?: boolean;
  encoding?: BufferEncoding;
}

/**
 * CSV Writer utility class for writing arrays of objects to CSV files
 */
export class CsvWriter {
  private static readonly DEFAULT_OPTIONS: ICsvWriterOptions = {
    delimiter: ',',
    includeHeaders: true,
    encoding: 'utf8'
  };

  /**
   * Write array of objects to CSV file
   * @param data Array of objects to write
   * @param filePath Path where to write the CSV file
   * @param options CSV writer options
   */
  static async writeToFile<T extends Record<string, any>>(
    data: T[],
    filePath: string,
    options: ICsvWriterOptions = {}
  ): Promise<void> {
    try {
      const opts = { ...this.DEFAULT_OPTIONS, ...options };

      if (!data || data.length === 0) {
        logger.warn(`No data to write to CSV file: ${filePath}`);
        return;
      }

      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Get headers from options or from first object keys
      const headers = opts.headers || Object.keys(data[0]);

      // Build CSV content
      const csvLines: string[] = [];

      // Add headers if required
      if (opts.includeHeaders) {
        csvLines.push(this.formatCsvRow(headers, opts.delimiter!));
      }

      // Add data rows
      for (const row of data) {
        const values = headers.map(header => this.formatCsvValue(row[header]));
        csvLines.push(this.formatCsvRow(values, opts.delimiter!));
      }

      // Write to file
      const csvContent = csvLines.join('\n');
      fs.writeFileSync(filePath, csvContent, opts.encoding);

      logger.info(`Successfully wrote ${data.length} records to CSV file: ${filePath}`);
    } catch (error) {
      logger.error(`Error writing CSV file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Append array of objects to existing CSV file
   * @param data Array of objects to append
   * @param filePath Path to existing CSV file
   * @param options CSV writer options
   */
  static async appendToFile<T extends Record<string, any>>(
    data: T[],
    filePath: string,
    options: ICsvWriterOptions = {}
  ): Promise<void> {
    try {
      const opts = { ...this.DEFAULT_OPTIONS, ...options };

      if (!data || data.length === 0) {
        logger.warn(`No data to append to CSV file: ${filePath}`);
        return;
      }

      // Get headers from options or from first object keys
      const headers = opts.headers || Object.keys(data[0]);

      // Build CSV content for appending (no headers)
      const csvLines: string[] = [];

      // Add data rows
      for (const row of data) {
        const values = headers.map(header => this.formatCsvValue(row[header]));
        csvLines.push(this.formatCsvRow(values, opts.delimiter!));
      }

      // Append to file
      const csvContent = csvLines.join('\n') + '\n';
      fs.appendFileSync(filePath, csvContent, opts.encoding);

      logger.info(`Successfully appended ${data.length} records to CSV file: ${filePath}`);
    } catch (error) {
      logger.error(`Error appending to CSV file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Format a single CSV value (handle quotes, commas, newlines)
   * @param value Value to format
   * @returns Formatted CSV value
   */
  private static formatCsvValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }

    let stringValue = String(value);

    // If value contains delimiter, quotes, or newlines, wrap in quotes and escape existing quotes
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n') || stringValue.includes('\r')) {
      stringValue = stringValue.replace(/"/g, '""'); // Escape quotes by doubling them
      return `"${stringValue}"`;
    }

    return stringValue;
  }

  /**
   * Format a CSV row from array of values
   * @param values Array of values
   * @param delimiter Delimiter to use
   * @returns Formatted CSV row
   */
  private static formatCsvRow(values: string[], delimiter: string): string {
    return values.join(delimiter);
  }

  /**
   * Create a timestamped filename
   * @param baseName Base name for the file
   * @param extension File extension (default: 'csv')
   * @returns Timestamped filename
   */
  static createTimestampedFilename(baseName: string, extension: string = 'csv'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    return `${baseName}-${timestamp}.${extension}`;
  }

  /**
   * Ensure a directory exists for processed files
   * @param baseDir Base directory (default: 'processed')
   * @returns Full path to the processed directory
   */
  static ensureProcessedDirectory(baseDir: string = 'processed'): string {
    const processedDir = path.resolve(process.env.DATA_FOLDER || 'data', baseDir);
    if (!fs.existsSync(processedDir)) {
      fs.mkdirSync(processedDir, { recursive: true });
    }
    return processedDir;
  }
}

/**
 * Convenience function to write CSV file
 * @param data Array of objects to write
 * @param filePath Path where to write the CSV file
 * @param options CSV writer options
 */
export async function writeCSV<T extends Record<string, any>>(
  data: T[],
  filePath: string,
  options?: ICsvWriterOptions
): Promise<void> {
  return CsvWriter.writeToFile(data, filePath, options);
}

/**
 * Convenience function to append to CSV file
 * @param data Array of objects to append
 * @param filePath Path to existing CSV file
 * @param options CSV writer options
 */
export async function appendCSV<T extends Record<string, any>>(
  data: T[],
  filePath: string,
  options?: ICsvWriterOptions
): Promise<void> {
  return CsvWriter.appendToFile(data, filePath, options);
}
