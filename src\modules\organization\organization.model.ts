
import mongoose, { Schema } from 'mongoose';
import { Document } from 'mongoose';
import { IRevenueCategory, RevenueCategorySchema, RevenueCategory } from './revenue-category.model';

export interface IOrganization extends Document {
    userId: mongoose.Types.ObjectId;
    revenueCategory: IRevenueCategory[];
    isInclusiveofGst: boolean;
}

const OrganizationSchema = new Schema<IOrganization>({

    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true,
    },

    revenueCategory: {
        type: [RevenueCategorySchema],
        required: false,
        default: []
    },

    isInclusiveofGst: {
        type: Boolean,
        required: false,
        default: true
    }
}, { timestamps: true });

export const Organization = mongoose.model<IOrganization>('organizations', OrganizationSchema);