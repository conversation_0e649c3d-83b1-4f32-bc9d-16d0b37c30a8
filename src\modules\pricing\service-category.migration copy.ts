import { Types } from "mongoose";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { IService, Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { ClassType, SessionType } from "./pricing.model";
import { AppointmentType, IAppointmentType } from "./appointment-type.model";
import LoggerConfig from "../../common/logger/log.module";

const logger = LoggerConfig('service-category.migration');
interface ICsvServiceCategory {
  id: string;
  name: string;
  type: string;
  isActive?: boolean;
  image?: string;
  description?: string;
  shortDescription?: string;
}

const CsvToObjectKeyMapServiceCategory: Record<keyof ICsvServiceCategory, string> = {
  id: "id",
  name: "name",
  type: "type",
  isActive: "is active",
  image: "image",
  description: "description",
  shortDescription: "short description"
}

interface ICsvAppointmentType {
  id: string;
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive?: boolean;
  image?: string;
  serviceCategoryId: string;
  isFeatured?: boolean;
  description?: string;
  shortDescription?: string;
}

const CsvToObjectKeyMapAppointmentType: Record<keyof ICsvAppointmentType, string> = {
  id: "id",
  name: "name",
  durationInMinutes: "duration in minutes",
  onlineBookingAllowed: "online booking allowed",
  isActive: "is active",
  image: "image",
  serviceCategoryId: "service category Id",
  isFeatured: "is featured",
  description: "description",
  shortDescription: "short description"
}

/**
 * Validate service data from CSV
 * @param csvService Service data from CSV
 * @returns Array of validation error messages
 */
function validateServiceData(csvService: ICsvServiceCategory): string[] {
  const errors: string[] = [];

  if (!csvService.id) {
    errors.push("Service ID is required");
  }

  if (!csvService.name) {
    errors.push("Service name is required");
  }

  if (!csvService.type) {
    errors.push("Service type is required");
  } else if (!Object.values(ClassType).includes(csvService.type as ClassType)) {
    errors.push(`Invalid service type: ${csvService.type}. Valid values are: ${Object.values(ClassType).join(', ')}`);
  }

  return errors;
}

/**
 * Migrate services data from CSV to MongoDB
 */
export async function migrateServices(_dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting services migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get services data from CSV
      const csvServices = await parseCSV<ICsvServiceCategory>(`Star-home-services.csv`, CsvToObjectKeyMapServiceCategory);

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvServices.forEach((service, index) => {
        const errors = validateServiceData(service);
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error("Validation errors in services data:", validationErrors);
        throw new Error("Invalid services data in CSV");
      }

      // Get appointment types data to include during service creation
      const csvAppointmentTypes = await parseCSV<ICsvAppointmentType>("service-home-test-prod.csv", CsvToObjectKeyMapAppointmentType);


      // Group appointment types by service ID
      const appointmentTypesByServiceId: { [serviceId: string]: IAppointmentType[] } = {};
      const appointmentTypes: IAppointmentType[] = [];

      const existingAppointmentTypes = await AppointmentType.find({ organizationId: global.config.organizationId }).session(session);
      const existingAppointmentTypeIds = existingAppointmentTypes.map(apt => apt.id);
      const existingAppointmentMap = new Map<string, IAppointmentType>();
      existingAppointmentTypes.forEach(apt => {
        existingAppointmentMap.set(apt._id.toString(), apt);
        if (apt.id) {
          existingAppointmentMap.set(apt.id.toString(), apt);
        }
      });
      csvAppointmentTypes.forEach((appointmentType: ICsvAppointmentType) => {
        if (existingAppointmentTypeIds.includes(appointmentType.id)) {
          logger.log(`Appointment type ${appointmentType.id} already exists. Skipping...`);
          return;
        }
      });

      const existingServices = await Service.find({ organizationId: global.config.organizationId }).session(session);
      const existingServiceIds = existingServices.map(s => s.id);
      const existingServiceMap = new Map<string, IService>();
      existingServices.forEach(s => {
        existingServiceMap.set(s._id.toString(), s);
        if (s.id) {
          existingServiceMap.set(s.id.toString(), s);
        }
      });


      csvAppointmentTypes.forEach((appointmentType: ICsvAppointmentType) => {

        const apt = new AppointmentType({
          organizationId: global.config.organizationId,
          id: appointmentType.id,
          name: appointmentType.name,
          durationInMinutes: appointmentType.durationInMinutes,
          onlineBookingAllowed: !!appointmentType.onlineBookingAllowed,
          isActive: !!appointmentType.isActive,
          image: appointmentType.image || "",
          isFeatured: !!appointmentType.isFeatured,
          shortDescription: appointmentType.shortDescription,
          description: appointmentType.description || ""
        });
        appointmentTypes.push(apt);

        appointmentType.serviceCategoryId.split(",").forEach((serviceId) => {
          if (!appointmentTypesByServiceId[serviceId]) {
            appointmentTypesByServiceId[serviceId] = [];
          }
          appointmentTypesByServiceId[serviceId].push(apt);
        });
      });

      // Transform data for MongoDB
      const services: IService[] = csvServices.map((csvService) => {
        const serviceAppointmentTypes = appointmentTypesByServiceId[csvService.id] || [];

        return new Service({
          id: csvService.id,
          name: csvService.name,
          description: csvService.description || "",
          shortDescription: csvService.shortDescription || "",
          image: csvService.image || "",
          classType: csvService.type as ClassType,
          appointmentType: serviceAppointmentTypes, // Include appointment types during creation
          isFeatured: !false,
          isActive: !!csvService.isActive,
          createdBy: global.config.organizationId,
          organizationId: global.config.organizationId
        });
      });

      // Insert services
      const appointmentResult = await AppointmentType.insertMany(appointmentTypes, { ordered: true, session });
      const servicesResult = await Service.insertMany(services, { ordered: true, session });
      logger.log(`Successfully migrated ${servicesResult.length} services to MongoDB`);

      // Log appointment types added to each service
      servicesResult.forEach((service, index) => {
        const csvService = csvServices[index];
        const appointmentTypesCount = service.appointmentType.length;
        logger.log(`Service "${service.name}" (CSV ID: ${csvService.id}) created with ${appointmentTypesCount} appointment types`);

        if (appointmentTypesCount > 0) {
          service.appointmentType.forEach((apt: IAppointmentType, aptIndex: number) => {
            logger.log(`  ${aptIndex + 1}. ${apt.name} (${apt.durationInMinutes} min)`);
          });
        }
      });

      // Create a mapping of CSV ID to MongoDB ObjectId for reference
      const serviceIdMapping: { [csvId: string]: string } = {};
      servicesResult.forEach((service, index) => {
        serviceIdMapping[csvServices[index].id] = service._id.toString();
      });

      logger.log("Service ID mapping (CSV ID -> MongoDB ObjectId):");
      Object.entries(serviceIdMapping).forEach(([csvId, mongoId]) => {
        logger.log(`  ${csvId} -> ${mongoId}`);
      });

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating services:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
