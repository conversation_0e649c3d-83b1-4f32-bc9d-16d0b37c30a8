user id,service category,service,package id,purchase id,date,start time,session,endtime,room id,is checkin
1,1,1,1,1,1,2023-07-11,09:00,1,10:00,true
Note*,
- Required fields are user id, date, start time, end time, session, package id.
- Purchase is optional. if purchase id is not provided it will use a default purchase id.
- Service and Service Category is option. if not provided it will use a first service and service category of the package.
- Checkin time is optional. if not provided it will use the start time and create a checkin record.