import mongoose, { Document, Schema, Types } from 'mongoose';
import { ClassType } from './pricing.model';
import { AppointmentType, AppointmentTypeSchema, IAppointmentType } from './appointment-type.model';

export interface IService extends Document {
  id: string;
  organizationId: Types.ObjectId;
  name: string;
  description?: string;
  onlineBookingAllowed?: boolean;
  shortDescription?: string;
  image?: string;
  classType: ClassType;
  appointmentType: IAppointmentType[];
  createdBy: Types.ObjectId;
  isActive?: boolean;
  isFeatured?: boolean;
  createdAt?: Date;
  orderIndex?: number;
  updatedAt?: Date;
}

// Define appointment type schema
// const AppointmentTypeSchema = new Schema<IAppointmentType>({
//   name: { type: String, required: true },
//   durationInMinutes: { type: Number, required: true },
//   onlineBookingAllowed: { type: Boolean, required: true },
//   isActive: { type: Boolean, default: true },
//   image: { type: String, required: false },
//   isFeatured: { type: Boolean, default: false }
// }, {
//   timestamps: false
// });

// Define service schema
const ServiceSchema = new Schema<IService>({
  id: {
    type: String,
    indexes: true,
    unique: true
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: "User"
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ""
  },

  onlineBookingAllowed: {
    type: Boolean,
    required: true,
    default: true
  },

  shortDescription: {
    type: String,
    default: ""
  },
  image: {
    type: String,
    required: false
  },
  classType: {
    type: String,
    required: true,
    enum: Object.values(ClassType)
  },
  appointmentType: [AppointmentTypeSchema],
  createdBy: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: "User"
  },

  isActive: {
    type: Boolean,
    default: true
  },

  isFeatured: {
    type: Boolean,
    required: false
  },
  orderIndex: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Create and export the model
export const SERVICE_COLLECTION = 'services';
export const Service = mongoose.model<IService>('Service', ServiceSchema, SERVICE_COLLECTION);

ServiceSchema.index({ id: 1, organizationId: 1 }, { unique: true, sparse: true });
