// models/scheduling.model.js
const mongoose = require("mongoose");
const { Schema } = mongoose;
const { ObjectId } = Schema.Types;

// Adjust these paths to where your enums live in the Express project
const { DateRange } = require("../utils/enums/date-range-enum");
const { ScheduleStatusType } = require("../enums/schedule-status.enum");
const { ClassType } = require("../utils/enums/class-type.enum");

const schedulingSchema = new Schema(
    {
        scheduledBy: { type: ObjectId, required: true, ref: "User" },

        facilityId: { type: ObjectId, required: true, ref: "Facility", index: true },

        trainerId: { type: ObjectId, required: false, ref: "User", index: true },

        organizationId: { type: ObjectId, required: true, ref: "User", index: true },

        clientId: {
            type: ObjectId,
            ref: "User",
            index: true,
            required: function () {
                // required unless classType is COURSES or CLASSES
                return this.classType !== ClassType.COURSES && this.classType !== ClassType.CLASSES;
            },
        },

        packageId: {
            type: ObjectId,
            ref: "Pricing",
            index: true,
            required: function () {
                // required unless classType is CLASSES
                return this.classType !== ClassType.CLASSES;
            },
        },

        purchaseId: {
            type: ObjectId,
            ref: "Purchase",
            index: true,
            required: function () {
                return this.classType !== ClassType.COURSES && this.classType !== ClassType.CLASSES;
            },
        },

        classType: { type: String, required: true, index: true }, // keep as string (enum validated below)

        subTypeId: { type: ObjectId, required: true, ref: "Attributes", index: true },

        serviceCategoryId: { type: ObjectId, required: true, ref: "Services", index: true },

        roomId: { type: ObjectId, required: false, ref: "Room", default: null, index: true },

        classCapacity: {
            type: Number,
            default: 0,
            min: 0,
            required: function () {
                return this.classType === ClassType.COURSES || this.classType === ClassType.CLASSES;
            },
        },

        dateRange: {
            type: String,
            enum: Object.values(DateRange || {}),
            required: false,
            default: DateRange ? DateRange.SINGLE : undefined,
        },

        duration: { type: Number, required: true },

        sessions: { type: Number, required: true },

        date: { type: Date, required: true, index: true },

        from: {
            type: String,
            required: false,
            match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
            index: true,
        },

        to: {
            type: String,
            required: false,
            match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
            index: true,
        },

        scheduleStatus: {
            type: String,
            enum: Object.values(ScheduleStatusType || {}),
            default: ScheduleStatusType ? ScheduleStatusType.BOOKED : undefined,
            required: false,
            index: true,
        },

        notes: { type: String, required: false },

        canceledBy: {
            type: ObjectId,
            ref: "User",
            index: true,
            required: false,
            default: undefined,
        },

        canceledAt: {
            type: Date,
            index: true,
            required: false,
            default: undefined,
        },

        // createdAt / updatedAt will be provided by timestamps option (no need to declare here)
    },
    {
        timestamps: true,
    }
);

// Optional: ensure the classType and dateRange enums are actually validated if enum imports are missing
// (This makes the model more resilient if the enum files are not present in the express project.)
if (!DateRange) {
    schedulingSchema.path("dateRange").enum([]);
}
if (!ScheduleStatusType) {
    schedulingSchema.path("scheduleStatus").enum([]);
}

// Pre-save hook: when canceledBy is modified, set canceledAt
schedulingSchema.pre("save", function (next) {
    // 'this' is the doc
    if (this.isModified && typeof this.isModified === "function") {
        try {
            if (this.isModified("canceledBy")) {
                this.canceledAt = new Date();
            }
        } catch (err) {
            return next(err);
        }
    }
    return next();
});

const Scheduling = mongoose.model("Scheduling", schedulingSchema);

module.exports = Scheduling;
