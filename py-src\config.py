import os
import dotenv
from bson import ObjectId

dotenv.load_dotenv()

MIGRATION_ID = os.getenv('MIGRATION_ID', None)
DATA_FOLDER = os.getenv('DATA_FOLDER', 'data')

MYSQL_CONFIG = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'climb_central'),
}

MONGO_CONFIG = {
    'uri': os.getenv('MONGODB_URI', 'mongodb://localhost:27017'),
    'database': os.getenv('DATABASE_NAME', 'development'),
}

ORGANIZATION_ID = ObjectId(os.getenv('ORGANIZATION_ID', None)) if os.getenv('ORGANIZATION_ID', None) else None


AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', None)
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', None)
AWS_S3_BUCKET = os.getenv('AWS_S3_BUCKET', None)
AWS_S3_REGION = os.getenv('AWS_S3_REGION', None)

S3_ENDPOINT = os.getenv('S3_ENDPOINT', None)
