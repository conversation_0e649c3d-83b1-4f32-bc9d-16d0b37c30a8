import mongoose, { Schema, Types, Document, model } from 'mongoose';
import { ProductType } from './products.model';

export enum InventoryActionType {
    RESTOCK = 'restock',
    ADJUSTMENT = 'adjustment',
    BULK_RESTOCK = 'bulk_restock',
}


// ---------------------
// Types and Model
// ---------------------
export interface IInventoryHistoryEntry {
    actionType: InventoryActionType;
    previousQty?: number;
    changeQty?: number;
    newQty?: number;
    newMrp?: number;
    previousMrp?: number;
    notes?: string;
    performedBy: Types.ObjectId;
    at: Date;
}

export interface IInventory extends Document {
    productType: ProductType;
    storeId: Types.ObjectId;
    productId: Types.ObjectId;
    promotion?: Types.ObjectId | null;
    productVariantId?: Types.ObjectId;
    salePrice: number;
    mrp: number;
    quantity: number;
    expiryDate?: Date | null;
    discount?: number;
    discountPrice: number;
    createdBy?: Types.ObjectId;
    organizationId: Types.ObjectId;
    history: IInventoryHistoryEntry[];
    createdAt: Date;
    updatedAt: Date;
}

// ---------------------
// Subschema: History Entry
// ---------------------
const InventoryHistoryEntrySchema = new Schema(
    {
        actionType: {
            type: String,
            enum: Object.values(InventoryActionType),
            required: true,
        },
        previousQty: Number,
        changeQty: Number,
        newQty: Number,
        newMrp: Number,
        previousMrp: Number,
        notes: String,
        performedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        at: {
            type: Date,
            default: Date.now,
        },
    },
    { _id: false } // No separate _id for subdoc
);

// ---------------------
// Main Schema: Inventory
// ---------------------
const InventorySchema = new Schema(
    {
        productType: {
            type: String,
            enum: Object.values(ProductType),
            required: true,
        },
        storeId: {
            type: Schema.Types.ObjectId,
            ref: 'facility',
            required: true,
            index: true,
        },
        productId: {
            type: Schema.Types.ObjectId,
            ref: 'Product',
            required: true,
            index: true,
        },
        promotion: {
            type: Schema.Types.ObjectId,
            ref: 'Promotion',
            default: null,
        },
        productVariantId: {
            type: Schema.Types.ObjectId,
            ref: 'ProductVariant',
            index: true,
        },
        salePrice: {
            type: Number,
            default: 0,
        },
        mrp: {
            type: Number,
            required: true,
        },
        quantity: {
            type: Number,
            required: true,
        },
        expiryDate: {
            type: Date,
            default: null,
        },
        discount: {
            type: Number,
            default: 0,
        },
        discountPrice: {
            type: Number,
            required: true,
            default: 0,
        },
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            index: true,
        },
        organizationId: {
            type: Schema.Types.ObjectId,
            ref: 'Organization',
            required: true,
            index: true,
        },
        history: {
            type: [InventoryHistoryEntrySchema],
            default: [],
        },
    },
    {
        timestamps: true,
    }
);

// Add indexes
InventorySchema.index({ _id: 1, 'history.at': -1 });
InventorySchema.index({ _id: 1, 'history.actionType': 1, 'history.at': -1 });
InventorySchema.index({ storeId: 1, productId: 1 });

export const Inventory = model<IInventory>('Inventory', InventorySchema);
