import { Types } from 'mongoose';
import { Invoice } from './invoice.model';
import { Purchase } from './purchase.model';
// import { InvoiceCalculationService } from './invoice-calculation.service';
import { DurationUnit } from '../pricing/pricing.model';
import { PaymentStatus } from '../../utils/enums/payment.enum';
import { PaymentMethod } from '../../utils/enums/paymentMethod.enum';
import { SessionType } from '../../utils/enums/session-type.enum';
import LoggerConfig from '../../common/logger/log.module';
import { connectToMongo } from '../../common/database/db.module';
import { User } from '../user/user.model';
import { ENUM_DISCOUNT_TYPE } from '../../common/enums/enums';


const logger = LoggerConfig('purchase.service');

// DTO interfaces based on the provided CreateInvoicePurchaseDto and CSV format
interface PaymentDetailsDTO {
  paymentMethod: string;
  paymentMethodId?: Types.ObjectId; // Made optional since it might not be in the CSV
  transactionId?: string;
  amount: number;
  paymentDate: Date;
  paymentStatus: string;
  paymentGateway?: string;
  description?: string;
  denominations?: Record<number, number>;
}

export interface CreateInvoicePurchaseDto {
  invoiceId: number;
  userId: Types.ObjectId;
  organizationId?: string; // Made optional since it might be derived from context
  facilityId: Types.ObjectId;
  purchaseItems?: {
    packageId: Types.ObjectId;
    quantity: number;
    isBundledPricing?: boolean;
  }[];
  productsItem?: {
    packageId: Types.ObjectId;
    quantity: number;
  }[];
  customPackageItems?: {
    customPackageId: string;
    quantity: number;
  }[];
  cartDiscount?: number;
  cartDiscountType?: ENUM_DISCOUNT_TYPE;
  subTotal: number;
  total: number;
  amountPaid?: number;
  platform?: string; // Made optional since it might not be in the CSV
  paymentDetails?: PaymentDetailsDTO[];
  isSplittedPayment?: boolean;
  billingAddressId?: string; // Made optional since it might not be in the CSV

  // Additional fields from CSV
  invoiceDate?: Date;
}

/**
 * Service for handling purchase operations
 */
export class PurchaseService {
  private InvoiceModel = Invoice;
  private PurchaseModel = Purchase;
  private PricingModel: any; // This would be imported from pricing module
  private InventoryModel: any; // This would be imported from inventory module
  private CustomPackageModel: any; // This would be imported from custom package module
  private UserModel: any; // This would be imported from user module
  private ClientModel: any; // This would be imported from client module
  private MembershipModel: any; // This would be imported from membership module

  constructor() {
    // Initialize models and dependencies
    // In a real implementation, these would be injected
  }

  /**
   * Create purchases from CSV data
   * @param csvData CSV data containing invoice and purchase information
   * @param user Current user
   * @returns Created invoices and purchases
   */
  async createPurchasesFromCsv(csvData: any[], user: any): Promise<any> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const row of csvData) {
      try {
        // Extract organization ID from user or environment
        const organizationId = process.env.ORGANIZATION_ID || user.organizationId;

        // Create payment details from CSV row
        const paymentDetails = [{
          paymentMethod: row.paymentMethod,
          paymentMethodId: row.paymentMethodId || new Types.ObjectId().toString(), // Generate ID if not provided
          transactionId: row.transactionId || "",
          amount: Number(row.amount),
          paymentDate: new Date(row.paymentDate),
          paymentStatus: row.paymentStatus,
          paymentGateway: row.paymentGateway || "",
        }];

        // Create purchase items from CSV row
        const purchaseItems = [{
          packageId: row.packageId,
          quantity: Number(row.quantity),
          isBundledPricing: false // Default to false unless specified
        }];

        // Create purchase DTO
        const purchaseDto: CreateInvoicePurchaseDto = {
          invoiceId: row.invoiceId,
          userId: row.userId,
          organizationId,
          facilityId: row.facilityId,
          purchaseItems,
          subTotal: Number(row.subTotal),
          total: Number(row.total),
          cartDiscount: row.cartDiscount ? Number(row.cartDiscount) : 0,
          cartDiscountType: row.cartDiscountType || ENUM_DISCOUNT_TYPE.PERCENTAGE,
          paymentDetails,
          invoiceDate: row.invoiceDate ? new Date(row.invoiceDate) : new Date(),
          platform: row.platform || "web",
          amountPaid: Number(row.amount),
          // Use a default billing address ID if not provided
          billingAddressId: row.billingAddressId || user._id.toString()
        };

        // Validate payment details with less strict rules for CSV
        this.validatePaymentDetails(paymentDetails, false, true);

        // Create the purchase
        await this.createPurchase(purchaseDto, user);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Error processing row ${row.invoiceId}: ${error.message}`);
        logger.error(`Error processing row ${row.invoiceId}:`, error);
      }
    }

    return results;
  }

  /**
   * Create a purchase with invoice and purchase items
   * @param createPurchaseDto Purchase data
   * @param user Current user
   * @returns Created invoice and purchase details
   */
  async createPurchase(createPurchaseDto: CreateInvoicePurchaseDto, session?: any): Promise<any> {
    // Start a database session for transaction
    const mongoose = require('mongoose');

    if (!session) {
      session = await mongoose.startSession();
      session.startTransaction();
    }

    try {
      const {
        userId,
        organizationId,
        facilityId,
        cartDiscount,
        platform,
        paymentDetails,
        billingAddressId,
        isSplittedPayment,
        amountPaid,
        cartDiscountType
      } = createPurchaseDto;

      // Get items from DTO
      const productsItem = (createPurchaseDto.productsItem || []) as {
        packageId: Types.ObjectId;
        quantity: number;
      }[];

      const purchaseItems = (createPurchaseDto.purchaseItems || []) as {
        packageId: Types.ObjectId;
        quantity: number;
        isBundledPricing?: boolean;
        bundledPricingId?: string;
      }[];

      const customPackageItems = (createPurchaseDto.customPackageItems || []) as {
        customPackageId: string;
        quantity: number;
      }[];

      // Validate inputs
      if ((!purchaseItems || purchaseItems.length === 0) &&
        (!productsItem || productsItem.length === 0) &&
        (!customPackageItems || customPackageItems.length === 0)) {
        throw new Error("Purchase/Product items are required");
      }

      if (!userId) {
        throw new Error("User ID is required");
      }

      // Format payment details
      const paymentDetailsData = paymentDetails?.map((detail: any) => ({
        paymentMethod: detail.paymentMethod,
        paymentMethodId: detail.paymentMethodId,
        transactionId: detail.transactionId || "",
        amount: detail.amount,
        paymentDate: detail.paymentDate,
        paymentStatus: detail.paymentStatus,
        paymentGateway: detail.paymentGateway || "",
        description: detail.description,
        denominations: detail.paymentMethod === PaymentMethod.CASH ? detail.denominations || {} : {},
      }));

      // Validate payment details
      this.validatePaymentDetails(paymentDetailsData, isSplittedPayment || false, false);

      // Get billing details
      let pipeline = await this.billingDetails(userId, facilityId);
      const billingDetails = await User.aggregate(pipeline).exec();

      if (!billingDetails || billingDetails.length === 0) {
        throw new Error("Insufficient data for billing details");
      }

      // Determine if this is a business purchase
      let clientBillingDetails = billingDetails[0]?.clientDetails;
      let isForBusiness = false;

      if (billingDetails[0].clientBusinessDetails &&
        billingDetails[0].clientBusinessDetails?._id?.toString() === billingAddressId) {
        clientBillingDetails = billingDetails[0].clientBusinessDetails;
        isForBusiness = true;
        if (!clientBillingDetails.gstNumber) {
          throw new Error("Please add GST number first for business related purchases");
        }
      }

      // Validate billing details
      this.checkRequiredFields([
        {
          clientDetails: clientBillingDetails,
          billingDetails: billingDetails[0]?.billingDetails,
        },
      ]);

      // Initialize billing objects
      let productBilling: any = {};
      let customPackageBilling: any = {};

      // Validate packages
      const packageIds = purchaseItems.map(item => new Types.ObjectId(item.packageId));
      let checkValidPackage = await this.PricingModel.find({ _id: { $in: packageIds }, isActive: true })
        .lean()
        .exec();

      // Validate products
      const productId = productsItem.map(item => new Types.ObjectId(item.packageId));
      let checkProductInInventory = await this.InventoryModel.find({ _id: { $in: productId } })
        .lean()
        .exec();

      // Validate custom packages
      const customPackageIds = customPackageItems.map(item => new Types.ObjectId(item.customPackageId));
      let checkValidCustomPackage = await this.CustomPackageModel
        .find({ _id: { $in: customPackageIds }, isActive: true })
        .lean()
        .exec();

      // Calculate effective totals for proportional discount allocation
      let productEffectiveTotal = 0;
      let packageEffectiveTotal = 0;
      let customPackageEffectiveTotal = 0;

      // Calculate product effective total
      for (const item of productsItem) {
        const inventory = checkProductInInventory.find(p => p._id.toString() === item.packageId.toString());
        if (!inventory) continue;

        const unitPrice = Number(inventory.salePrice || 0);
        const discount = (Number(inventory.discount || 0) / 100) * unitPrice;
        const effective = unitPrice - discount;
        productEffectiveTotal += effective * Number(item.quantity || 1);
      }

      // Calculate package effective total
      for (const item of purchaseItems) {
        const pkg = checkValidPackage.find(p => p._id.toString() === item.packageId.toString());
        if (!pkg) continue;

        const unitPrice = Number(pkg?.price || 0);
        let discount = 0;

        if (pkg?.discount?.type === ENUM_DISCOUNT_TYPE.FLAT) {
          discount = Number(pkg.discount.value || 0);
        } else if (pkg?.discount?.type === ENUM_DISCOUNT_TYPE.PERCENTAGE) {
          discount = (Number(pkg.discount.value || 0) / 100) * unitPrice;
        }

        const effective = unitPrice - discount;
        packageEffectiveTotal += effective * Number(item.quantity || 1);
      }

      // Calculate custom package effective total
      for (const item of customPackageItems) {
        const pkg = checkValidCustomPackage.find(p => p._id.toString() === item.customPackageId.toString());
        if (!pkg) continue;

        const unitPrice = Number(pkg?.unitPrice || 0);
        let discount = 0;

        if (pkg?.discount?.type === ENUM_DISCOUNT_TYPE.FLAT) {
          discount = Number(pkg.discount.value || 0);
        } else if (pkg?.discount?.type === ENUM_DISCOUNT_TYPE.PERCENTAGE) {
          discount = (Number(pkg.discount.value || 0) / 100) * unitPrice;
        }

        const effective = unitPrice - discount;
        customPackageEffectiveTotal += effective * Number(item.quantity || 1);
      }

      const totalEffective = productEffectiveTotal + packageEffectiveTotal + customPackageEffectiveTotal;

      // Calculate proportional cart discount
      let productCartDiscount = 0;
      let packageCartDiscount = 0;
      let customPackageCartDiscount = 0;

      if (cartDiscountType === ENUM_DISCOUNT_TYPE.FLAT && totalEffective > 0) {
        productCartDiscount = (productEffectiveTotal / totalEffective) * (cartDiscount || 0);
        packageCartDiscount = (packageEffectiveTotal / totalEffective) * (cartDiscount || 0);
        customPackageCartDiscount = (customPackageEffectiveTotal / totalEffective) * (cartDiscount || 0);
      } else if (cartDiscountType === ENUM_DISCOUNT_TYPE.PERCENTAGE) {
        productCartDiscount = cartDiscount || 0;
        packageCartDiscount = cartDiscount || 0;
        customPackageCartDiscount = cartDiscount || 0;
      }

      // Process products if any
      if (checkProductInInventory.length !== productId.length && productsItem.length > 0) {
        throw new Error("Some products are invalid");
      } else if (checkProductInInventory.length === productId.length && productsItem.length > 0) {
        productBilling = await this.purchaseProduct(
          productsItem,
          productCartDiscount,
          cartDiscountType || ENUM_DISCOUNT_TYPE.PERCENTAGE,
          productEffectiveTotal
        );
      }

      // Process custom packages if any
      if (customPackageItems.length > 0 && checkValidCustomPackage.length !== customPackageIds.length) {
        throw new Error("Some custom packages are invalid");
      } else if (customPackageItems.length > 0 && checkValidCustomPackage.length === customPackageIds.length) {
        customPackageBilling = await this.purchaseCustomPackage(
          customPackageItems,
          customPackageCartDiscount,
          cartDiscountType || ENUM_DISCOUNT_TYPE.PERCENTAGE,
          customPackageEffectiveTotal,
          checkValidCustomPackage
        );
      }

      // Validate packages
      if (checkValidPackage.length !== packageIds.length) {
        throw new Error("Some packages are invalid or inactive");
      }

      // Handle bundled packages
      let newPurchaseItems = [];
      let membershipId = "";
      let bundledPackageIds = purchaseItems
        .filter(item => item?.isBundledPricing === true)
        .map(item => new Types.ObjectId(item.packageId));

      if (bundledPackageIds?.length > 0) {
        for (const item of purchaseItems) {
          if (bundledPackageIds.some(id => id.toString() === item.packageId.toString())) {
            let packageDetails = checkValidPackage.find(pkg => pkg._id.toString() === item.packageId.toString());
            if (!packageDetails) continue;

            const checkBundledPackage = await this.PricingModel.find({
              _id: { $in: packageDetails.pricingIds.map(id => new Types.ObjectId(id)) },
              isActive: true,
            }).exec();

            if (checkBundledPackage.length !== packageDetails.pricingIds.length) {
              throw new Error("Some packages are invalid or inactive in bundled pricing");
            }

            checkValidPackage = [...checkValidPackage, ...checkBundledPackage];

            for (const id of packageDetails.pricingIds) {
              newPurchaseItems.push({
                packageId: id,
                quantity: item.quantity,
                isBundledPricing: true,
                bundledPricingId: item.packageId,
              });
            }
          } else {
            newPurchaseItems.push({
              packageId: item.packageId,
              quantity: item.quantity,
              isBundledPricing: false,
            });
          }
        }
      } else {
        newPurchaseItems = [...purchaseItems];
      }

      // Get next invoice number and order ID
      const highestInvoice = await this.InvoiceModel.findOne(
        { organizationId, facilityId },
        { invoiceNumber: 1 }
      ).sort({ invoiceNumber: -1 }).lean();

      const invoiceNumber = highestInvoice ? highestInvoice.invoiceNumber + 1 : 1;

      const highestOrderId = await this.InvoiceModel.findOne(
        { organizationId, facilityId },
        { orderId: 1 }
      ).sort({ orderId: -1 }).lean();

      const orderId = highestOrderId?.orderId ? highestOrderId?.orderId + 1 : 1;

      // Calculate invoice totals
      let invoiceSubTotal = 0;
      let invoiceDiscountExcludeCart = 0;
      let invoiceDiscountIncludeCart = 0;
      let invoiceGstAmount = 0;
      let invoiceAmountAfterGst = 0;
      let roundOff: any;
      let grandTotal = 0;
      let totalFlatCartDiscountAllocated = 0;

      // Process each purchase item
      for (let i = 0; i < purchaseItems.length; i++) {
        const item = purchaseItems[i];
        const packageDetails = checkValidPackage.find(pkg => pkg._id.toString() === item.packageId.toString());

        if (!packageDetails) {
          throw new Error(`Package with ID ${item.packageId} not found`);
        }

        const { durationUnit, expiredInDays: expireIn } = packageDetails;

        if (!durationUnit || expireIn == null) {
          throw new Error(`Invalid duration unit or expiration days in package ${item.packageId}`);
        }

        // Calculate start and end dates
        const startDate = new Date();
        const endDate = new Date(startDate);

        switch (durationUnit) {
          case DurationUnit.DAYS:
            endDate.setDate(endDate.getDate() + expireIn);
            break;
          case DurationUnit.MONTHS:
            endDate.setMonth(endDate.getMonth() + expireIn);
            break;
          case DurationUnit.YEARS:
            endDate.setFullYear(endDate.getFullYear() + expireIn);
            break;
          default:
            throw new Error(`Invalid duration unit: ${durationUnit}`);
        }

        // Calculate discounts and totals
        let discountExcludeCart = 0;
        let discountIncludeCart = 0;
        let gstAmount = 0;
        let totalAmountExcludeCartDiscount = 0;

        // Calculate item discount
        if (packageDetails?.discount?.type === ENUM_DISCOUNT_TYPE.PERCENTAGE) {
          discountExcludeCart = Number(item.quantity) * Number(
            ((Number(packageDetails?.discount?.value) / 100) * Number(packageDetails?.price)).toFixed(2)
          );
        } else if (packageDetails?.discount?.type === ENUM_DISCOUNT_TYPE.FLAT) {
          discountExcludeCart = Number(item.quantity) * Number(Number(packageDetails?.discount?.value).toFixed(2));
        }

        const totalUnitPrice = Number(item.quantity) * Number(packageDetails?.price);
        invoiceSubTotal += totalUnitPrice;
        invoiceDiscountExcludeCart += discountExcludeCart;
        totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;

        // Calculate cart discount
        if (cartDiscount && cartDiscountType === ENUM_DISCOUNT_TYPE.PERCENTAGE) {
          discountIncludeCart = Number(
            ((Number(cartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2)
          );
          gstAmount = Number(
            (totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageDetails?.tax) / 100)
          );
          invoiceGstAmount += gstAmount;
          invoiceDiscountIncludeCart += discountIncludeCart;
        } else if (packageCartDiscount && cartDiscountType === ENUM_DISCOUNT_TYPE.FLAT) {
          const denominator = productEffectiveTotal + packageEffectiveTotal;
          const itemShare = denominator > 0 ? totalAmountExcludeCartDiscount / denominator : 0;

          // Handle last item for accurate rounding
          if (i === purchaseItems.length - 1) {
            discountIncludeCart = Number((Number(packageCartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
          } else {
            discountIncludeCart = Number((itemShare * Number(packageCartDiscount)).toFixed(2));
            totalFlatCartDiscountAllocated += discountIncludeCart;
          }

          invoiceDiscountIncludeCart += discountIncludeCart;
          gstAmount = Number(
            (totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageDetails?.tax) / 100)
          );
          invoiceGstAmount += gstAmount;
        } else {
          gstAmount = Number((totalAmountExcludeCartDiscount * (Number(packageDetails?.tax) / 100)).toFixed(2));
          invoiceGstAmount += gstAmount;
        }

        // Update purchase item with calculated values
        item["packageName"] = packageDetails?.name;
        item["expireIn"] = packageDetails?.expiredInDays;
        item["durationUnit"] = packageDetails?.durationUnit;
        item["startDate"] = startDate;
        item["endDate"] = endDate;
        item["unitPrice"] = packageDetails?.price;
        item["discountType"] = packageDetails?.discount?.type;
        item["discountValue"] = packageDetails?.discount?.value;
        item["discountExcludeCart"] = discountExcludeCart?.toFixed(2);
        item["discountIncludeCart"] = discountIncludeCart?.toFixed(2);
        item["hsnOrSacCode"] = packageDetails?.hsnOrSacCode;
        item["tax"] = packageDetails?.tax;
        item["gstAmount"] = gstAmount.toFixed(2) || 0;

        // Check for membership
        if (packageDetails?.membershipId) {
          membershipId = packageDetails?.membershipId || "";
        }
      }

      // Calculate final invoice totals
      invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiceDiscountIncludeCart + invoiceDiscountExcludeCart) || 0;

      // Add product billing totals if any
      if (checkProductInInventory.length === productId.length && productsItem.length > 0) {
        invoiceSubTotal += productBilling?.invoiceSubTotal;
        invoiceDiscountExcludeCart += productBilling?.invoiveDiscountExculdeCart;
        invoiceDiscountIncludeCart += productBilling?.invoiveDiscountIncludeCart;
        invoiceGstAmount += productBilling?.invoiceGstAmount;
        invoiceAmountAfterGst += productBilling?.invoiceAmountAfterGst;
      }

      // Add custom package billing totals if any
      if (customPackageItems.length > 0) {
        invoiceSubTotal += customPackageBilling?.invoiceSubTotal;
        invoiceDiscountExcludeCart += customPackageBilling?.invoiveDiscountExculdeCart;
        invoiceDiscountIncludeCart += customPackageBilling?.invoiveDiscountIncludeCart;
        invoiceGstAmount += customPackageBilling?.invoiceGstAmount;
        invoiceAmountAfterGst += customPackageBilling?.invoiceAmountAfterGst;
      }

      // Calculate round off and grand total
      roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
      grandTotal = Math.floor(invoiceAmountAfterGst);

      // Create invoice object
      const invoice = new this.InvoiceModel({
        createdBy: global.config.organizationId,
        invoiceNumber,
        orderId,
        userId,
        organizationId,
        facilityId,
        purchaseItems,
        productItem: productBilling?.productDetail || [],
        customPackageItems: customPackageBilling?.productDetail || [],
        subTotal: invoiceSubTotal?.toFixed(2),
        discount: invoiceDiscountExcludeCart?.toFixed(2),
        cartDiscount: cartDiscount || 0,
        cartDiscountType: cartDiscountType,
        cartDiscountAmount: invoiceDiscountIncludeCart?.toFixed(2) || 0,
        totalGstValue: invoiceGstAmount?.toFixed(2) || 0,
        totalAmountAfterGst: invoiceAmountAfterGst.toFixed(2),
        roundOff: roundOff,
        grandTotal: grandTotal.toFixed(2),
        // amountInWords: InvoiceCalculationService.convertAmountToWords(Math.floor(grandTotal)),
        invoiceDate: new Date(),
        platform,
        paymentStatus: paymentDetailsData[0].paymentStatus,
        paymentDetails: paymentDetailsData,
        isSplittedPayment,
        amountPaid,
        clientDetails: {
          customerId: billingDetails[0]?.clientDetails?.customerId || "",
          name: billingDetails[0]?.clientDetails?.name || "",
          email: billingDetails[0]?.clientDetails?.email || "",
          phone: billingDetails[0]?.clientDetails?.phone || "",
        },
        clientBillingDetails: {
          customerId: clientBillingDetails?.customerId || "",
          name: clientBillingDetails?.name || "",
          addressLine1: clientBillingDetails?.addressLine1 || "",
          addressLine2: clientBillingDetails?.addressLine2 || "",
          postalCode: clientBillingDetails?.postalCode || "",
          cityId: new Types.ObjectId(clientBillingDetails?.cityId) || "",
          cityName: clientBillingDetails?.cityName || "",
          stateId: new Types.ObjectId(clientBillingDetails?.stateId) || "",
          stateName: clientBillingDetails?.stateName || "",
          gstNumber: clientBillingDetails?.gstNumber,
          utCode: clientBillingDetails?.utCode || "",
        },
        billingDetails: {
          facilityName: billingDetails[0]?.billingDetails?.facilityName || "",
          billingName: billingDetails[0]?.billingDetails?.billingName || "",
          gstNumber: billingDetails[0]?.billingDetails?.gstNumber || "",
          email: billingDetails[0]?.billingDetails?.email || "",
          phone: billingDetails[0]?.billingDetails?.phone || "",
          addressLine1: billingDetails[0]?.billingDetails?.addressLine1 || "",
          addressLine2: billingDetails[0]?.billingDetails?.addressLine2 || "",
          postalCode: billingDetails[0]?.billingDetails?.postalCode || "",
          cityId: new Types.ObjectId(billingDetails[0]?.billingDetails?.cityId) || "",
          cityName: billingDetails[0]?.billingDetails?.cityName || "",
          stateId: new Types.ObjectId(billingDetails[0]?.billingDetails?.stateId) || "",
          stateName: billingDetails[0]?.billingDetails?.stateName || "",
          utCode: billingDetails[0]?.billingDetails?.utCode || "",
        },
        isForBusiness,
      });

      // Save the invoice
      const savedInvoice = await invoice.save({ session });
      const invoiceId = savedInvoice._id;

      // Create purchase records
      const purchases = [];
      for (const item of newPurchaseItems) {
        const packageDetails = checkValidPackage.find(pkg => pkg._id.toString() === item.packageId.toString());
        if (!packageDetails) {
          throw new Error(`Package with ID ${item.packageId} not found`);
        }

        // Check for membership
        if (packageDetails?.membershipId) {
          membershipId = packageDetails?.membershipId || "";
        }

        // Create a purchase record for each quantity
        for (let i = 0; i < item.quantity; i++) {
          const { durationUnit, expiredInDays: expireIn } = packageDetails;

          if (!durationUnit || expireIn == null) {
            throw new Error(`Invalid duration unit or expiration days in package ${item.packageId}`);
          }

          // Calculate start and end dates
          const startDate = new Date();
          const endDate = new Date(startDate);

          switch (durationUnit) {
            case DurationUnit.DAYS:
              endDate.setDate(endDate.getDate() + expireIn);
              break;
            case DurationUnit.MONTHS:
              endDate.setMonth(endDate.getMonth() + expireIn);
              break;
            case DurationUnit.YEARS:
              endDate.setFullYear(endDate.getFullYear() + expireIn);
              break;
            default:
              throw new Error(`Invalid duration unit: ${durationUnit}`);
          }

          // Create purchase object
          purchases.push({
            invoiceId,
            packageId: item.packageId,
            userId,
            organizationId,
            facilityId,
            ...(item?.isBundledPricing === true && {
              bundledPricingId: item.bundledPricingId,
            }),
            purchasedBy: global.config.organizationId,
            membershipId: packageDetails?.membershipId ? packageDetails?.membershipId : null,
            purchaseDate: new Date(),
            paymentStatus: paymentDetailsData[0].paymentStatus,
            isExpired: false,
            sessionType: packageDetails.sessionType,
            ...(packageDetails.sessionType === (SessionType.UNLIMITED || SessionType.DAY_PASS) && {
              sessionPerDay: packageDetails.sessionPerDay,
            }),
            ...(packageDetails.sessionType === SessionType.DAY_PASS && {
              dayPassLimit: packageDetails.dayPassLimit,
            }),
            totalSessions: packageDetails.sessionType === SessionType.SINGLE ? 1 : packageDetails.sessionCount,
            sessionConsumed: 0,
            startDate: startDate,
            endDate: endDate,
          });
        }
      }

      // Save purchase records
      if (purchases.length > 0) {
        await this.PurchaseModel.insertMany(purchases, { session });
      }

      // Update membership if needed
      if (membershipId !== "") {
        const clientProfile = await this.ClientModel.findOne({ userId: userId });
        if (!clientProfile?.membershipId) {
          let checkCounter = await this.MembershipModel.findById(membershipId);

          if (!checkCounter?.lastcounter || checkCounter?.lastcounter === 0) {
            await this.ClientModel.findOneAndUpdate(
              { userId: userId },
              { membershipId: `${checkCounter.prefix}-${checkCounter.counter}`.toString() },
              { session }
            );
            await this.MembershipModel.findByIdAndUpdate(
              membershipId,
              { lastcounter: checkCounter.counter + 1 },
              { session }
            );
          } else {
            await this.ClientModel.findOneAndUpdate(
              { userId: userId },
              { membershipId: `${checkCounter.prefix}-${checkCounter.lastcounter}` },
              { session }
            );
            await this.MembershipModel.findByIdAndUpdate(
              membershipId,
              { lastcounter: checkCounter.lastcounter + 1 },
              { session }
            );
          }
        }
      }

      // Commit the transaction
      await session.commitTransaction();

      // Generate invoice PDF if payment is not pending
      if (paymentDetailsData[0].paymentStatus !== PaymentStatus.PENDING) {
        // In a real implementation, you would call an invoice service to generate a PDF
        // await this.invoiceService.generateInvoice(invoice, organizationEmail);
      }

      return {
        message: "Purchase created successfully",
        invoiceId,
        purchaseCount: purchases.length
      };
    } catch (error) {
      // Rollback the transaction on error
      await session.abortTransaction();
      throw new Error(error.message);
    } finally {
      // Close the session
      session.endSession();
    }
  }

  /**
   * Validate required fields for billing
   * @param data Data to validate
   * @throws Error if required fields are missing
   */
  private checkRequiredFields(data: any[]): void {
    for (const item of data) {
      const { clientDetails, billingDetails } = item;

      // Check client details
      if (!clientDetails?.name) throw new Error('Client name is required');
      if (!clientDetails?.customerId) throw new Error('Client ID is required');

      // Check billing details
      if (!billingDetails?.facilityName) throw new Error('Facility name is required');
      if (!billingDetails?.billingName) throw new Error('Billing name is required');
      if (!billingDetails?.email) throw new Error('Billing email is required');
      if (!billingDetails?.phone) throw new Error('Billing phone is required');
      if (!billingDetails?.cityId) throw new Error('City is required');
      if (!billingDetails?.cityName) throw new Error('City name is required');
      if (!billingDetails?.stateId) throw new Error('State is required');
      if (!billingDetails?.stateName) throw new Error('State name is required');
      if (!billingDetails?.utCode) throw new Error('UT code is required');
    }
  }

  /**
   * Validate payment details and denominations
   * @param paymentDetails Payment details to validate
   * @param isSplittedPayment Whether payment is split
   * @param isFromCsv Whether the data is from CSV (less strict validation)
   * @throws Error if validation fails
   */
  private validatePaymentDetails(
    paymentDetails: PaymentDetailsDTO[],
    isSplittedPayment: boolean,
    isFromCsv: boolean = false
  ): void {
    if (!paymentDetails || paymentDetails.length === 0) {
      throw new Error('Payment details are required');
    }

    // Validate each payment detail
    for (const detail of paymentDetails) {
      if (!detail.paymentMethod) throw new Error('Payment method is required');

      // Less strict validation for CSV data
      if (!isFromCsv && !detail.paymentMethodId) throw new Error('Payment method ID is required');

      if (!detail.amount) throw new Error('Payment amount is required');
      if (!detail.paymentDate) throw new Error('Payment date is required');
      if (!detail.paymentStatus) throw new Error('Payment status is required');

      // Validate denominations for cash payments (skip for CSV data)
      if (!isFromCsv && detail.paymentMethod === PaymentMethod.CASH && !isSplittedPayment) {
        if (!detail.denominations || Object.keys(detail.denominations).length === 0) {
          throw new Error('Denominations are required for cash payment when split payment is disabled');
        }

        // Validate denomination values
        const validDenominations = [2000, 500, 200, 100, 50, 20, 10, 5, 2, 1];
        for (const key of Object.keys(detail.denominations)) {
          if (!validDenominations.includes(Number(key))) {
            throw new Error(`Invalid denomination: ${key}. Valid denominations are: ${validDenominations.join(', ')}`);
          }
        }
      }

      // Validate payment gateway for non-cash payments (less strict for CSV)
      if (!isFromCsv && detail.paymentMethod !== PaymentMethod.CASH && !detail.paymentGateway) {
        throw new Error('Payment gateway is required for non-cash payments');
      }
    }
  }

  /**
   * Process product items for purchase
   * @param productsItem Product items to purchase
   * @param productCartDiscount Cart discount for products
   * @param cartDiscountType Type of cart discount
   * @param productEffectiveTotal Effective total for products
   * @returns Product billing details
   */
  private async purchaseProduct(
    productsItem: { packageId: Types.ObjectId; quantity: number }[],
    productCartDiscount: number,
    cartDiscountType: string,
    productEffectiveTotal: number
  ): Promise<any> {
    try {
      // In a real implementation, fetch product details from database
      const productIds = productsItem.map(item => new Types.ObjectId(item.packageId));
      const inventoryItems = await this.InventoryModel.find({ _id: { $in: productIds } }).lean();

      // Calculate product billing using the calculation service
      // return InvoiceCalculationService.calculateProductBilling(
      //   productsItem,
      //   productCartDiscount,
      //   cartDiscountType,
      //   productEffectiveTotal,
      //   inventoryItems
      // );
    } catch (error) {
      logger.error('Error processing product items:', error);
      throw error;
    }
  }

  /**
   * Process custom package items for purchase
   * @param customPackageItems Custom package items to purchase
   * @param customPackageCartDiscount Cart discount for custom packages
   * @param cartDiscountType Type of cart discount
   * @param customPackageEffectiveTotal Effective total for custom packages
   * @param customPackages Custom packages data
   * @returns Custom package billing details
   */
  private async purchaseCustomPackage(
    customPackageItems: { customPackageId: string; quantity: number }[],
    customPackageCartDiscount: number,
    cartDiscountType: string,
    customPackageEffectiveTotal: number,
    customPackages: any[]
  ): Promise<any> {
    try {
      // return InvoiceCalculationService.calculateCustomPackageBilling(
      //   customPackageItems,
      //   customPackageCartDiscount,
      //   cartDiscountType,
      //   customPackageEffectiveTotal,
      //   customPackages
      // );
    } catch (error) {
      logger.error('Error processing custom package items:', error);
      throw error;
    }
  }

  async billingDetails(userId: any, facilityId: any) {
    let pipeline: any = [
      {
        $match: {
          _id: new Types.ObjectId(userId),
        },
      },
      {
        $lookup: {
          from: "clients",
          localField: "_id",
          foreignField: "userId",
          as: "clientDetails",
        },
      },
      {
        $unwind: {
          path: "$clientDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "states",
          localField: "clientDetails.address.state",
          foreignField: "_id",
          as: "clientDetails.address.stateDetails",
        },
      },
      {
        $lookup: {
          from: "cities",
          localField: "clientDetails.address.city",
          foreignField: "_id",
          as: "clientDetails.address.cityDetails",
        },
      },
      {
        $lookup: {
          from: "states",
          localField: "clientDetails.businessAddress.state",
          foreignField: "_id",
          as: "clientDetails.businessAddress.stateDetails",
        },
      },
      {
        $lookup: {
          from: "cities",
          localField: "clientDetails.businessAddress.city",
          foreignField: "_id",
          as: "clientDetails.businessAddress.cityDetails",
        },
      },
      {
        $unwind: {
          path: "$clientDetails.businessAddress.stateDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientDetails.businessAddress.cityDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientDetails.address.stateDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$clientDetails.address.cityDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "facilities",
          let: { facilityId: new Types.ObjectId(facilityId) },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$facilityId"],
                },
              },
            },
            {
              $lookup: {
                from: "states",
                localField: "billingDetails.state",
                foreignField: "_id",
                as: "billingDetails.stateDetails",
              },
            },
            {
              $lookup: {
                from: "cities",
                localField: "billingDetails.city",
                foreignField: "_id",
                as: "billingDetails.cityDetails",
              },
            },
            {
              $unwind: {
                path: "$billingDetails.stateDetails",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $unwind: {
                path: "$billingDetails.cityDetails",
                preserveNullAndEmptyArrays: true,
              },
            },
          ],
          as: "facilityDetails",
        },
      },
      {
        $unwind: {
          path: "$facilityDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          clientDetails: {
            _id: "$clientDetails.address._id",
            userId: "$clientDetails._id",
            customerId: "$clientDetails.clientId",
            name: { $concat: ["$firstName", " ", "$lastName"] },
            email: "$email",
            phone: "$mobile",
            addressLine1: "$clientDetails.address.addressLine1",
            addressLine2: "$clientDetails.address.addressLine2",
            postalCode: "$clientDetails.address.postalCode",
            cityId: "$clientDetails.address.city",
            cityName: "$clientDetails.address.cityDetails.name",
            stateId: "$clientDetails.address.state",
            stateName: "$clientDetails.address.stateDetails.name",
            utCode: "$clientDetails.address.stateDetails.gstCode",
          },
          clientBusinessDetails: {
            _id: "$clientDetails.businessAddress._id",
            userId: "$clientDetails._id",
            customerId: "$clientDetails.clientId",
            name: "$clientDetails.businessAddress.businessName",
            email: "$email",
            phone: "$mobile",
            addressLine1: "$clientDetails.businessAddress.addressLine1",
            addressLine2: "$clientDetails.businessAddress.addressLine2",
            postalCode: "$clientDetails.businessAddress.postalCode",
            cityId: "$clientDetails.businessAddress.city",
            cityName: "$clientDetails.businessAddress.cityDetails.name",
            stateId: "$clientDetails.businessAddress.state",
            stateName: "$clientDetails.businessAddress.stateDetails.name",
            gstNumber: "$clientDetails.businessAddress.gstNumber",
            utCode: "$clientDetails.businessAddress.stateDetails.gstCode",
          },
          billingDetails: {
            _id: "$facilityDetails.billingDetails._id",
            facilityName: "$facilityDetails.facilityName",
            billingName: "$facilityDetails.billingDetails.billingName",
            gstNumber: "$facilityDetails.billingDetails.gstNumber",
            email: "$facilityDetails.email",
            phone: "$facilityDetails.mobile",
            addressLine1: "$facilityDetails.billingDetails.addressLine1",
            addressLine2: "$facilityDetails.billingDetails.addressLine2",
            postalCode: "$facilityDetails.billingDetails.postalCode",
            cityId: "$facilityDetails.billingDetails.city",
            cityName: "$facilityDetails.billingDetails.cityDetails.name",
            stateId: "$facilityDetails.billingDetails.state",
            stateName: "$facilityDetails.billingDetails.stateDetails.name",
            utCode: "$facilityDetails.billingDetails.stateDetails.gstCode",
          },
        },
      },
    ];
    return pipeline;
  }
}