#!/usr/bin/env python3
"""
Test script for update-expiry.py functionality
"""

import sys
import os
import importlib.util
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the module with hyphen in name
spec = importlib.util.spec_from_file_location("update_expiry", "py-src/update-expiry.py")
update_expiry = importlib.util.module_from_spec(spec)
spec.loader.exec_module(update_expiry)

calculate_end_date = update_expiry.calculate_end_date
MongoDBHelper = update_expiry.MongoDBHelper

def test_calculate_end_date():
    """Test the calculate_end_date function"""
    print("Testing calculate_end_date function...")
    
    # Test data
    start_date = datetime(2024, 1, 1, 12, 0, 0)
    
    # Test days
    result = calculate_end_date(start_date, 30, 'days')
    expected = start_date + timedelta(days=30)
    assert result == expected, f"Days test failed: {result} != {expected}"
    print("✓ Days calculation test passed")
    
    # Test months (approximate)
    result = calculate_end_date(start_date, 2, 'months')
    expected = start_date + timedelta(days=60)  # 2 months * 30 days
    assert result == expected, f"Months test failed: {result} != {expected}"
    print("✓ Months calculation test passed")
    
    # Test years (approximate)
    result = calculate_end_date(start_date, 1, 'years')
    expected = start_date + timedelta(days=365)  # 1 year * 365 days
    assert result == expected, f"Years test failed: {result} != {expected}"
    print("✓ Years calculation test passed")
    
    # Test unknown unit (should default to days)
    result = calculate_end_date(start_date, 10, 'unknown')
    expected = start_date + timedelta(days=10)
    assert result == expected, f"Unknown unit test failed: {result} != {expected}"
    print("✓ Unknown unit test passed")
    
    # Test None start date
    result = calculate_end_date(None, 30, 'days')
    assert result is None, f"None start date test failed: {result} != None"
    print("✓ None start date test passed")
    
    print("All calculate_end_date tests passed! ✓")

def test_mongodb_helper():
    """Test the MongoDBHelper class (without actual DB connection)"""
    print("\nTesting MongoDBHelper class...")
    
    # Test initialization
    helper = MongoDBHelper()
    assert helper.client is None, "Client should be None initially"
    assert helper.db is None, "DB should be None initially"
    print("✓ MongoDBHelper initialization test passed")
    
    print("MongoDBHelper basic tests passed! ✓")

def main():
    """Run all tests"""
    print("Running update-expiry.py tests...\n")
    
    try:
        test_calculate_end_date()
        test_mongodb_helper()
        print("\n🎉 All tests passed successfully!")
        return 0
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
