import { Command } from 'commander';
import { migrateInvoices } from '../modules/invoice/invoice.migration';

export function createInvoiceCommand(): Command {
  const invoiceCommand = new Command('invoice');

  invoiceCommand
    .description('Migrate invoice and purchase data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .option('-m, --mode <mode>', 'Migration mode: create, update, or upsert', 'create')
    .action(async (options) => {
      try {
        // Validate mode option
        const validModes = ['create', 'update', 'upsert'];
        if (!validModes.includes(options.mode)) {
          console.error(`Invalid mode: ${options.mode}. Valid modes are: ${validModes.join(', ')}`);
          process.exit(1);
        }

        await migrateInvoices(options.database, options.mode);
      } catch (error) {
        console.error('Error executing invoice migration command:', error);
        process.exit(1);
      }
    });

  return invoiceCommand;
}
